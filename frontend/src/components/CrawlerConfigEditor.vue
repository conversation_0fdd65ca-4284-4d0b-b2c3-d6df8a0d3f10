<template>
  <div class="crawler-config-editor">
    <el-card shadow="never" class="config-card">
      <template #header>
        <div class="card-header">
          <h3>爬虫配置管理</h3>
          <div class="header-actions">
            <el-button @click="resetConfig" :disabled="loading">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
            <el-button @click="saveConfig" type="primary" :loading="loading">
              <el-icon><Check /></el-icon>
              保存配置
            </el-button>
          </div>
        </div>
      </template>

      <el-tabs v-model="activeTab" class="config-tabs">
        <!-- 基础配置 -->
        <el-tab-pane label="基础配置" name="basic">
          <el-form :model="config" :rules="rules" ref="basicFormRef" label-width="140px">
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="API基础URL" prop="base_url">
                  <el-input 
                    v-model="config.base_url" 
                    placeholder="https://api.example.com"
                    clearable
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="API访问令牌" prop="api_token">
                  <el-input 
                    v-model="config.api_token" 
                    type="password" 
                    placeholder="请输入API访问令牌"
                    show-password
                    clearable
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="24">
              <el-col :span="8">
                <el-form-item label="请求超时(秒)" prop="timeout">
                  <el-input-number 
                    v-model="config.timeout" 
                    :min="5" 
                    :max="300" 
                    :step="5"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="请求间隔(秒)" prop="request_delay">
                  <el-input-number 
                    v-model="config.request_delay" 
                    :min="0.1" 
                    :max="10" 
                    :step="0.1"
                    :precision="1"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="最大重试次数" prop="max_retries">
                  <el-input-number 
                    v-model="config.max_retries" 
                    :min="0" 
                    :max="10" 
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-form-item label="并发限制" prop="concurrent_limit">
              <el-slider 
                v-model="config.concurrent_limit" 
                :min="1" 
                :max="20" 
                :step="1"
                show-stops
                show-input
                style="width: 300px"
              />
              <el-text type="info" size="small" style="margin-left: 16px">
                建议值：3-8，过高可能导致API限流
              </el-text>
            </el-form-item>
          </el-form>
        </el-tab-pane>

        <!-- 请求头配置 -->
        <el-tab-pane label="请求头配置" name="headers">
          <div class="headers-config">
            <div class="section-header">
              <h4>HTTP请求头</h4>
              <el-button @click="addHeader" type="primary" size="small">
                <el-icon><Plus /></el-icon>
                添加请求头
              </el-button>
            </div>
            
            <div class="headers-list">
              <div v-for="(header, index) in config.headers" :key="index" class="header-item">
                <el-row :gutter="12" align="middle">
                  <el-col :span="8">
                    <el-input 
                      v-model="header.key" 
                      placeholder="请求头名称"
                      size="small"
                    />
                  </el-col>
                  <el-col :span="12">
                    <el-input 
                      v-model="header.value" 
                      placeholder="请求头值"
                      size="small"
                    />
                  </el-col>
                  <el-col :span="4">
                    <el-button @click="removeHeader(index)" type="danger" size="small" plain>
                      <el-icon><Delete /></el-icon>
                    </el-button>
                  </el-col>
                </el-row>
              </div>
            </div>

            <el-alert 
              title="常用请求头示例" 
              type="info" 
              :closable="false"
              style="margin-top: 16px"
            >
              <div>User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36</div>
              <div>Accept: application/json, text/plain, */*</div>
              <div>Content-Type: application/json</div>
            </el-alert>
          </div>
        </el-tab-pane>

        <!-- 代理配置 -->
        <el-tab-pane label="代理配置" name="proxy">
          <el-form :model="config" label-width="140px">
            <el-form-item label="启用代理">
              <el-switch v-model="config.proxy_enabled" />
            </el-form-item>

            <template v-if="config.proxy_enabled">
              <el-row :gutter="24">
                <el-col :span="8">
                  <el-form-item label="代理类型">
                    <el-select v-model="config.proxy_type" style="width: 100%">
                      <el-option label="HTTP" value="http" />
                      <el-option label="HTTPS" value="https" />
                      <el-option label="SOCKS5" value="socks5" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="代理主机">
                    <el-input v-model="config.proxy_host" placeholder="127.0.0.1" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="代理端口">
                    <el-input-number 
                      v-model="config.proxy_port" 
                      :min="1" 
                      :max="65535"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="24">
                <el-col :span="12">
                  <el-form-item label="代理用户名">
                    <el-input v-model="config.proxy_username" placeholder="可选" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="代理密码">
                    <el-input 
                      v-model="config.proxy_password" 
                      type="password" 
                      placeholder="可选"
                      show-password
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-form-item>
                <el-button @click="testProxy" :loading="testingProxy">
                  <el-icon><Connection /></el-icon>
                  测试代理连接
                </el-button>
              </el-form-item>
            </template>
          </el-form>
        </el-tab-pane>

        <!-- 调度配置 -->
        <el-tab-pane label="调度配置" name="schedule">
          <el-form :model="config" label-width="140px">
            <el-form-item label="启用定时任务">
              <el-switch v-model="config.schedule_enabled" />
            </el-form-item>

            <template v-if="config.schedule_enabled">
              <el-form-item label="执行频率">
                <el-radio-group v-model="config.schedule_type">
                  <el-radio value="daily">每日执行</el-radio>
                  <el-radio value="weekly">每周执行</el-radio>
                  <el-radio value="monthly">每月执行</el-radio>
                  <el-radio value="custom">自定义</el-radio>
                </el-radio-group>
              </el-form-item>

              <el-form-item v-if="config.schedule_type === 'daily'" label="执行时间">
                <el-time-picker 
                  v-model="config.schedule_time" 
                  format="HH:mm"
                  placeholder="选择执行时间"
                />
              </el-form-item>

              <el-form-item v-if="config.schedule_type === 'weekly'" label="执行日期">
                <el-select v-model="config.schedule_weekday" placeholder="选择星期几">
                  <el-option label="星期一" :value="1" />
                  <el-option label="星期二" :value="2" />
                  <el-option label="星期三" :value="3" />
                  <el-option label="星期四" :value="4" />
                  <el-option label="星期五" :value="5" />
                  <el-option label="星期六" :value="6" />
                  <el-option label="星期日" :value="0" />
                </el-select>
              </el-form-item>

              <el-form-item v-if="config.schedule_type === 'custom'" label="Cron表达式">
                <el-input 
                  v-model="config.schedule_cron" 
                  placeholder="0 2 * * *"
                />
                <el-text type="info" size="small">
                  格式：分 时 日 月 周，例如 "0 2 * * *" 表示每天凌晨2点执行
                </el-text>
              </el-form-item>

              <el-form-item label="时间窗口">
                <el-row :gutter="12">
                  <el-col :span="12">
                    <el-time-picker 
                      v-model="config.schedule_start_time" 
                      format="HH:mm"
                      placeholder="开始时间"
                    />
                  </el-col>
                  <el-col :span="12">
                    <el-time-picker 
                      v-model="config.schedule_end_time" 
                      format="HH:mm"
                      placeholder="结束时间"
                    />
                  </el-col>
                </el-row>
                <el-text type="info" size="small">
                  设置允许执行任务的时间窗口，避免在业务高峰期执行
                </el-text>
              </el-form-item>
            </template>
          </el-form>
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 配置预览 -->
    <el-card shadow="never" class="preview-card" style="margin-top: 16px">
      <template #header>
        <h4>配置预览</h4>
      </template>
      <el-input 
        v-model="configPreview" 
        type="textarea" 
        :rows="8" 
        readonly
        placeholder="配置预览将在这里显示..."
      />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { 
  Refresh, 
  Check, 
  Plus, 
  Delete, 
  Connection 
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance } from 'element-plus'
import { crawlerApi } from '../api/crawler'

// 响应式数据
const loading = ref(false)
const testingProxy = ref(false)
const activeTab = ref('basic')
const basicFormRef = ref<FormInstance>()

// 配置数据
const config = ref({
  // 基础配置
  base_url: 'https://api.example.com',
  api_token: '',
  timeout: 30,
  request_delay: 1.0,
  max_retries: 3,
  concurrent_limit: 5,
  
  // 请求头配置
  headers: [
    { key: 'User-Agent', value: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36' },
    { key: 'Accept', value: 'application/json, text/plain, */*' }
  ],
  
  // 代理配置
  proxy_enabled: false,
  proxy_type: 'http',
  proxy_host: '',
  proxy_port: 8080,
  proxy_username: '',
  proxy_password: '',
  
  // 调度配置
  schedule_enabled: false,
  schedule_type: 'daily',
  schedule_time: null,
  schedule_weekday: 1,
  schedule_cron: '0 2 * * *',
  schedule_start_time: null,
  schedule_end_time: null
})

// 表单验证规则
const rules = {
  base_url: [
    { required: true, message: '请输入API基础URL', trigger: 'blur' },
    { type: 'url', message: '请输入有效的URL', trigger: 'blur' }
  ],
  api_token: [
    { required: true, message: '请输入API访问令牌', trigger: 'blur' }
  ],
  timeout: [
    { required: true, message: '请设置请求超时时间', trigger: 'blur' }
  ],
  request_delay: [
    { required: true, message: '请设置请求间隔', trigger: 'blur' }
  ],
  max_retries: [
    { required: true, message: '请设置最大重试次数', trigger: 'blur' }
  ],
  concurrent_limit: [
    { required: true, message: '请设置并发限制', trigger: 'blur' }
  ]
}

// 计算属性
const configPreview = computed(() => {
  return JSON.stringify(config.value, null, 2)
})

// 方法
const loadConfig = async () => {
  try {
    loading.value = true
    const response = await crawlerApi.getConfig()
    if (response.success) {
      Object.assign(config.value, response.data)
    }
  } catch (error) {
    console.error('加载配置失败:', error)
    ElMessage.error('加载配置失败')
  } finally {
    loading.value = false
  }
}

const saveConfig = async () => {
  try {
    // 验证表单
    if (basicFormRef.value) {
      const valid = await basicFormRef.value.validate()
      if (!valid) {
        activeTab.value = 'basic'
        return
      }
    }

    loading.value = true
    const response = await crawlerApi.updateConfig(config.value)
    if (response.success) {
      ElMessage.success('配置保存成功')
    }
  } catch (error) {
    console.error('保存配置失败:', error)
    ElMessage.error('保存配置失败')
  } finally {
    loading.value = false
  }
}

const resetConfig = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要重置配置吗？这将恢复到默认设置。',
      '确认重置',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await loadConfig()
    ElMessage.success('配置已重置')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('重置配置失败:', error)
    }
  }
}

const addHeader = () => {
  config.value.headers.push({ key: '', value: '' })
}

const removeHeader = (index: number) => {
  config.value.headers.splice(index, 1)
}

const testProxy = async () => {
  try {
    testingProxy.value = true
    // 这里可以调用后端API测试代理连接
    await new Promise(resolve => setTimeout(resolve, 2000)) // 模拟测试
    ElMessage.success('代理连接测试成功')
  } catch (error) {
    ElMessage.error('代理连接测试失败')
  } finally {
    testingProxy.value = false
  }
}

// 组件挂载时加载配置
onMounted(() => {
  loadConfig()
})

// 暴露方法给父组件
defineExpose({
  saveConfig,
  resetConfig,
  loadConfig
})
</script>

<style scoped>
.crawler-config-editor {
  padding: 20px;
}

.config-card {
  border-radius: 8px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 500;
}

.config-tabs {
  margin-top: 16px;
}

.headers-config {
  padding: 16px 0;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
}

.headers-list {
  margin-bottom: 16px;
}

.header-item {
  margin-bottom: 12px;
}

.preview-card {
  border-radius: 8px;
}
</style>
