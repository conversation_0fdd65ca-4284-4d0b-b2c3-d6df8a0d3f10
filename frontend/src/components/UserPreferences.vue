<template>
  <div class="user-preferences">
    <el-card shadow="never" class="preferences-card">
      <template #header>
        <div class="card-header">
          <h3>用户设置</h3>
          <div class="header-actions">
            <el-button @click="resetToDefaults" type="info" size="small">
              <el-icon><Refresh /></el-icon>
              恢复默认
            </el-button>
            <el-button @click="savePreferences" type="primary" size="small" :loading="saving">
              <el-icon><Check /></el-icon>
              保存设置
            </el-button>
          </div>
        </div>
      </template>

      <el-tabs v-model="activeTab" type="card">
        <!-- 界面设置 -->
        <el-tab-pane label="界面设置" name="interface">
          <div class="settings-section">
            <h4>显示设置</h4>
            <el-form label-width="140px">
              <el-form-item label="默认视图模式">
                <el-radio-group v-model="preferences.interface.defaultView">
                  <el-radio value="enhanced">增强视图</el-radio>
                  <el-radio value="classic">经典视图</el-radio>
                </el-radio-group>
              </el-form-item>

              <el-form-item label="主题模式">
                <el-radio-group v-model="preferences.interface.theme">
                  <el-radio value="light">浅色主题</el-radio>
                  <el-radio value="dark">深色主题</el-radio>
                  <el-radio value="auto">跟随系统</el-radio>
                </el-radio-group>
              </el-form-item>

              <el-form-item label="语言设置">
                <el-select v-model="preferences.interface.language" style="width: 200px">
                  <el-option label="简体中文" value="zh-CN" />
                  <el-option label="繁體中文" value="zh-TW" />
                  <el-option label="English" value="en-US" />
                </el-select>
              </el-form-item>

              <el-form-item label="侧边栏折叠">
                <el-switch v-model="preferences.interface.sidebarCollapsed" />
              </el-form-item>

              <el-form-item label="显示面包屑">
                <el-switch v-model="preferences.interface.showBreadcrumb" />
              </el-form-item>

              <el-form-item label="显示页脚">
                <el-switch v-model="preferences.interface.showFooter" />
              </el-form-item>
            </el-form>

            <h4>表格设置</h4>
            <el-form label-width="140px">
              <el-form-item label="表格密度">
                <el-radio-group v-model="preferences.interface.tableSize">
                  <el-radio value="large">宽松</el-radio>
                  <el-radio value="default">默认</el-radio>
                  <el-radio value="small">紧凑</el-radio>
                </el-radio-group>
              </el-form-item>

              <el-form-item label="每页显示">
                <el-select v-model="preferences.interface.pageSize" style="width: 120px">
                  <el-option label="10条" :value="10" />
                  <el-option label="20条" :value="20" />
                  <el-option label="50条" :value="50" />
                  <el-option label="100条" :value="100" />
                </el-select>
              </el-form-item>

              <el-form-item label="显示斑马纹">
                <el-switch v-model="preferences.interface.tableStripe" />
              </el-form-item>

              <el-form-item label="显示边框">
                <el-switch v-model="preferences.interface.tableBorder" />
              </el-form-item>
            </el-form>
          </div>
        </el-tab-pane>

        <!-- 数据设置 -->
        <el-tab-pane label="数据设置" name="data">
          <div class="settings-section">
            <h4>默认筛选条件</h4>
            <el-form label-width="140px">
              <el-form-item label="默认年份">
                <el-select v-model="preferences.data.defaultYears" multiple style="width: 300px">
                  <el-option v-for="year in availableYears" :key="year" :label="year" :value="year" />
                </el-select>
              </el-form-item>

              <el-form-item label="默认省份">
                <el-select v-model="preferences.data.defaultProvinces" multiple style="width: 300px">
                  <el-option v-for="province in availableProvinces" :key="province.code" :label="province.name" :value="province.code" />
                </el-select>
              </el-form-item>

              <el-form-item label="默认科类">
                <el-checkbox-group v-model="preferences.data.defaultScienceTypes">
                  <el-checkbox value="理科">理科</el-checkbox>
                  <el-checkbox value="文科">文科</el-checkbox>
                  <el-checkbox value="综合">综合</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
            </el-form>

            <h4>数据刷新设置</h4>
            <el-form label-width="140px">
              <el-form-item label="自动刷新">
                <el-switch v-model="preferences.data.autoRefresh" />
              </el-form-item>

              <el-form-item label="刷新间隔" v-if="preferences.data.autoRefresh">
                <el-select v-model="preferences.data.refreshInterval" style="width: 150px">
                  <el-option label="30秒" :value="30" />
                  <el-option label="1分钟" :value="60" />
                  <el-option label="5分钟" :value="300" />
                  <el-option label="10分钟" :value="600" />
                </el-select>
              </el-form-item>

              <el-form-item label="缓存数据">
                <el-switch v-model="preferences.data.enableCache" />
              </el-form-item>

              <el-form-item label="缓存时间" v-if="preferences.data.enableCache">
                <el-input-number 
                  v-model="preferences.data.cacheTimeout" 
                  :min="1" 
                  :max="60"
                  style="width: 150px"
                />
                <span style="margin-left: 8px">分钟</span>
              </el-form-item>
            </el-form>
          </div>
        </el-tab-pane>

        <!-- 通知设置 -->
        <el-tab-pane label="通知设置" name="notifications">
          <div class="settings-section">
            <h4>系统通知</h4>
            <el-form label-width="140px">
              <el-form-item label="桌面通知">
                <el-switch v-model="preferences.notifications.desktop" />
              </el-form-item>

              <el-form-item label="声音提醒">
                <el-switch v-model="preferences.notifications.sound" />
              </el-form-item>

              <el-form-item label="任务完成通知">
                <el-switch v-model="preferences.notifications.taskComplete" />
              </el-form-item>

              <el-form-item label="任务失败通知">
                <el-switch v-model="preferences.notifications.taskFailed" />
              </el-form-item>

              <el-form-item label="系统错误通知">
                <el-switch v-model="preferences.notifications.systemError" />
              </el-form-item>
            </el-form>

            <h4>邮件通知</h4>
            <el-form label-width="140px">
              <el-form-item label="启用邮件通知">
                <el-switch v-model="preferences.notifications.email.enabled" />
              </el-form-item>

              <el-form-item label="邮箱地址" v-if="preferences.notifications.email.enabled">
                <el-input 
                  v-model="preferences.notifications.email.address" 
                  placeholder="请输入邮箱地址"
                  style="width: 300px"
                />
              </el-form-item>

              <el-form-item label="通知频率" v-if="preferences.notifications.email.enabled">
                <el-radio-group v-model="preferences.notifications.email.frequency">
                  <el-radio value="immediate">立即发送</el-radio>
                  <el-radio value="daily">每日汇总</el-radio>
                  <el-radio value="weekly">每周汇总</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-form>
          </div>
        </el-tab-pane>

        <!-- 高级设置 -->
        <el-tab-pane label="高级设置" name="advanced">
          <div class="settings-section">
            <h4>性能设置</h4>
            <el-form label-width="140px">
              <el-form-item label="启用虚拟滚动">
                <el-switch v-model="preferences.advanced.virtualScroll" />
                <el-text type="info" size="small" style="margin-left: 8px">
                  大数据量时提升性能
                </el-text>
              </el-form-item>

              <el-form-item label="预加载数据">
                <el-switch v-model="preferences.advanced.preloadData" />
              </el-form-item>

              <el-form-item label="图表动画">
                <el-switch v-model="preferences.advanced.chartAnimation" />
              </el-form-item>

              <el-form-item label="调试模式">
                <el-switch v-model="preferences.advanced.debugMode" />
              </el-form-item>
            </el-form>

            <h4>数据导出</h4>
            <el-form label-width="140px">
              <el-form-item label="默认导出格式">
                <el-select v-model="preferences.advanced.defaultExportFormat" style="width: 150px">
                  <el-option label="Excel" value="xlsx" />
                  <el-option label="CSV" value="csv" />
                  <el-option label="JSON" value="json" />
                </el-select>
              </el-form-item>

              <el-form-item label="包含表头">
                <el-switch v-model="preferences.advanced.exportHeaders" />
              </el-form-item>

              <el-form-item label="压缩导出文件">
                <el-switch v-model="preferences.advanced.compressExport" />
              </el-form-item>
            </el-form>

            <h4>开发者选项</h4>
            <el-form label-width="140px">
              <el-form-item label="显示API响应时间">
                <el-switch v-model="preferences.advanced.showApiTime" />
              </el-form-item>

              <el-form-item label="显示组件边界">
                <el-switch v-model="preferences.advanced.showComponentBounds" />
              </el-form-item>

              <el-form-item label="控制台日志级别">
                <el-select v-model="preferences.advanced.logLevel" style="width: 150px">
                  <el-option label="Error" value="error" />
                  <el-option label="Warn" value="warn" />
                  <el-option label="Info" value="info" />
                  <el-option label="Debug" value="debug" />
                </el-select>
              </el-form-item>
            </el-form>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import {
  Refresh,
  Check
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 响应式数据
const saving = ref(false)
const activeTab = ref('interface')

// 用户偏好设置
const preferences = reactive({
  interface: {
    defaultView: 'enhanced',
    theme: 'light',
    language: 'zh-CN',
    sidebarCollapsed: false,
    showBreadcrumb: true,
    showFooter: true,
    tableSize: 'default',
    pageSize: 20,
    tableStripe: true,
    tableBorder: false
  },
  data: {
    defaultYears: [2023, 2024],
    defaultProvinces: [],
    defaultScienceTypes: ['理科', '文科'],
    autoRefresh: true,
    refreshInterval: 60,
    enableCache: true,
    cacheTimeout: 10
  },
  notifications: {
    desktop: true,
    sound: true,
    taskComplete: true,
    taskFailed: true,
    systemError: true,
    email: {
      enabled: false,
      address: '',
      frequency: 'daily'
    }
  },
  advanced: {
    virtualScroll: true,
    preloadData: true,
    chartAnimation: true,
    debugMode: false,
    defaultExportFormat: 'xlsx',
    exportHeaders: true,
    compressExport: false,
    showApiTime: false,
    showComponentBounds: false,
    logLevel: 'info'
  }
})

// 可选数据
const availableYears = [2020, 2021, 2022, 2023, 2024]
const availableProvinces = [
  { code: 'BJ', name: '北京' },
  { code: 'SH', name: '上海' },
  { code: 'GD', name: '广东' },
  { code: 'JS', name: '江苏' },
  { code: 'ZJ', name: '浙江' },
  { code: 'SD', name: '山东' },
  { code: 'HN', name: '河南' },
  { code: 'SC', name: '四川' }
]

// 默认设置
const defaultPreferences = {
  interface: {
    defaultView: 'enhanced',
    theme: 'light',
    language: 'zh-CN',
    sidebarCollapsed: false,
    showBreadcrumb: true,
    showFooter: true,
    tableSize: 'default',
    pageSize: 20,
    tableStripe: true,
    tableBorder: false
  },
  data: {
    defaultYears: [2023, 2024],
    defaultProvinces: [],
    defaultScienceTypes: ['理科', '文科'],
    autoRefresh: true,
    refreshInterval: 60,
    enableCache: true,
    cacheTimeout: 10
  },
  notifications: {
    desktop: true,
    sound: true,
    taskComplete: true,
    taskFailed: true,
    systemError: true,
    email: {
      enabled: false,
      address: '',
      frequency: 'daily'
    }
  },
  advanced: {
    virtualScroll: true,
    preloadData: true,
    chartAnimation: true,
    debugMode: false,
    defaultExportFormat: 'xlsx',
    exportHeaders: true,
    compressExport: false,
    showApiTime: false,
    showComponentBounds: false,
    logLevel: 'info'
  }
}

// 方法
const loadPreferences = () => {
  try {
    const saved = localStorage.getItem('user-preferences')
    if (saved) {
      const savedPrefs = JSON.parse(saved)
      Object.assign(preferences, savedPrefs)
    }
  } catch (error) {
    console.error('加载用户设置失败:', error)
  }
}

const savePreferences = async () => {
  try {
    saving.value = true
    
    // 保存到本地存储
    localStorage.setItem('user-preferences', JSON.stringify(preferences))
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success('设置保存成功')
    
    // 应用设置
    applyPreferences()
  } catch (error) {
    console.error('保存用户设置失败:', error)
    ElMessage.error('保存设置失败')
  } finally {
    saving.value = false
  }
}

const resetToDefaults = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要恢复到默认设置吗？这将清除所有自定义配置。',
      '确认重置',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    Object.assign(preferences, JSON.parse(JSON.stringify(defaultPreferences)))
    ElMessage.success('已恢复默认设置')
  } catch (error) {
    // 用户取消操作
  }
}

const applyPreferences = () => {
  // 应用主题设置
  if (preferences.interface.theme === 'dark') {
    document.documentElement.classList.add('dark')
  } else {
    document.documentElement.classList.remove('dark')
  }
  
  // 应用语言设置
  // 这里可以集成i18n
  
  // 应用其他设置
  console.log('应用用户设置:', preferences)
}

// 组件挂载时加载设置
onMounted(() => {
  loadPreferences()
  applyPreferences()
})

// 暴露方法给父组件
defineExpose({
  savePreferences,
  resetToDefaults,
  preferences
})
</script>

<style scoped>
.user-preferences {
  padding: 20px;
}

.preferences-card {
  border-radius: 12px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.settings-section {
  padding: 16px 0;
}

.settings-section h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #409eff;
  border-bottom: 1px solid #e4e7ed;
  padding-bottom: 8px;
}

.settings-section:not(:last-child) {
  margin-bottom: 24px;
}
</style>
