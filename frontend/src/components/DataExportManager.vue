<template>
  <div class="data-export-manager">
    <el-card shadow="never" class="export-card">
      <template #header>
        <div class="card-header">
          <h3>数据导出管理</h3>
          <el-button @click="showExportDialog = true" type="primary">
            <el-icon><Download /></el-icon>
            新建导出任务
          </el-button>
        </div>
      </template>

      <!-- 导出任务列表 -->
      <div class="export-tasks">
        <el-table :data="exportTasks" v-loading="loading">
          <el-table-column prop="name" label="任务名称" min-width="150" />
          <el-table-column prop="dataType" label="数据类型" width="120">
            <template #default="{ row }">
              <el-tag :type="getDataTypeColor(row.dataType)" size="small">
                {{ getDataTypeText(row.dataType) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="format" label="导出格式" width="100">
            <template #default="{ row }">
              <el-tag size="small">{{ row.format.toUpperCase() }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusColor(row.status)" size="small">
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="progress" label="进度" width="120">
            <template #default="{ row }">
              <el-progress 
                v-if="row.status === 'processing'"
                :percentage="row.progress" 
                :stroke-width="6"
                :show-text="false"
              />
              <span v-else-if="row.status === 'completed'">100%</span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column prop="recordCount" label="记录数" width="100" />
          <el-table-column prop="fileSize" label="文件大小" width="100" />
          <el-table-column prop="createTime" label="创建时间" width="160">
            <template #default="{ row }">
              {{ formatTime(row.createTime) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <el-button-group size="small">
                <el-button
                  v-if="row.status === 'completed'"
                  @click="downloadFile(row)"
                  type="primary"
                  title="下载"
                >
                  <el-icon><Download /></el-icon>
                </el-button>
                <el-button
                  v-if="row.status === 'processing'"
                  @click="cancelExport(row)"
                  type="warning"
                  title="取消"
                >
                  <el-icon><Close /></el-icon>
                </el-button>
                <el-button
                  v-if="row.status === 'failed'"
                  @click="retryExport(row)"
                  type="info"
                  title="重试"
                >
                  <el-icon><Refresh /></el-icon>
                </el-button>
                <el-button
                  @click="deleteExport(row)"
                  type="danger"
                  title="删除"
                >
                  <el-icon><Delete /></el-icon>
                </el-button>
              </el-button-group>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>

    <!-- 导出配置对话框 -->
    <el-dialog 
      v-model="showExportDialog" 
      title="新建导出任务" 
      width="600px"
      @close="resetExportForm"
    >
      <el-form :model="exportForm" :rules="exportRules" ref="exportFormRef" label-width="120px">
        <el-form-item label="任务名称" prop="name">
          <el-input v-model="exportForm.name" placeholder="请输入任务名称" />
        </el-form-item>
        
        <el-form-item label="数据类型" prop="dataType">
          <el-radio-group v-model="exportForm.dataType">
            <el-radio value="universities">大学数据</el-radio>
            <el-radio value="majors">专业数据</el-radio>
            <el-radio value="admissions">录取数据</el-radio>
            <el-radio value="statistics">统计数据</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="导出格式" prop="format">
          <el-select v-model="exportForm.format" style="width: 100%">
            <el-option label="Excel (.xlsx)" value="xlsx" />
            <el-option label="CSV (.csv)" value="csv" />
            <el-option label="JSON (.json)" value="json" />
            <el-option label="PDF (.pdf)" value="pdf" />
          </el-select>
        </el-form-item>

        <el-form-item label="筛选条件">
          <el-tabs v-model="activeFilterTab" type="card">
            <el-tab-pane label="基础筛选" name="basic">
              <el-row :gutter="16">
                <el-col :span="12">
                  <el-form-item label="年份">
                    <el-select v-model="exportForm.filters.years" multiple placeholder="选择年份">
                      <el-option v-for="year in availableYears" :key="year" :label="year" :value="year" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="省份">
                    <el-select v-model="exportForm.filters.provinces" multiple placeholder="选择省份">
                      <el-option v-for="province in availableProvinces" :key="province.code" :label="province.name" :value="province.code" />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-tab-pane>
            
            <el-tab-pane label="高级筛选" name="advanced">
              <el-form-item label="分数范围">
                <el-slider
                  v-model="exportForm.filters.scoreRange"
                  range
                  :min="0"
                  :max="750"
                  :step="10"
                  show-stops
                  show-input
                />
              </el-form-item>
              
              <el-form-item label="排名范围">
                <el-input-number 
                  v-model="exportForm.filters.rankStart" 
                  placeholder="起始排名"
                  :min="1"
                  style="width: 45%"
                />
                <span style="margin: 0 8px">至</span>
                <el-input-number 
                  v-model="exportForm.filters.rankEnd" 
                  placeholder="结束排名"
                  :min="1"
                  style="width: 45%"
                />
              </el-form-item>
            </el-tab-pane>
          </el-tabs>
        </el-form-item>

        <el-form-item label="导出字段">
          <el-checkbox-group v-model="exportForm.fields">
            <el-checkbox 
              v-for="field in availableFields[exportForm.dataType]" 
              :key="field.value" 
              :value="field.value"
            >
              {{ field.label }}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <el-form-item label="导出选项">
          <el-checkbox v-model="exportForm.options.includeHeaders">包含表头</el-checkbox>
          <el-checkbox v-model="exportForm.options.compressFile">压缩文件</el-checkbox>
          <el-checkbox v-model="exportForm.options.splitLargeFiles">大文件分割</el-checkbox>
        </el-form-item>

        <el-form-item label="预计记录数">
          <el-statistic :value="estimatedRecords" suffix="条" />
          <el-text type="info" size="small">
            预计文件大小: {{ estimatedFileSize }}
          </el-text>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showExportDialog = false">取消</el-button>
        <el-button @click="previewExport" type="info">预览</el-button>
        <el-button @click="startExport" type="primary" :loading="exporting">
          开始导出
        </el-button>
      </template>
    </el-dialog>

    <!-- 预览对话框 -->
    <el-dialog v-model="showPreviewDialog" title="导出预览" width="800px">
      <div class="preview-content">
        <el-table :data="previewData" max-height="400">
          <el-table-column 
            v-for="field in selectedFields" 
            :key="field.value"
            :prop="field.value"
            :label="field.label"
            min-width="120"
          />
        </el-table>
        <div class="preview-footer">
          <el-text type="info">
            显示前10条记录，实际导出将包含所有符合条件的数据
          </el-text>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import {
  Download,
  Close,
  Refresh,
  Delete
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance } from 'element-plus'

// 响应式数据
const loading = ref(false)
const exporting = ref(false)
const showExportDialog = ref(false)
const showPreviewDialog = ref(false)
const activeFilterTab = ref('basic')
const exportFormRef = ref<FormInstance>()

// 导出任务列表
const exportTasks = reactive([
  {
    id: 1,
    name: '2023年大学数据导出',
    dataType: 'universities',
    format: 'xlsx',
    status: 'completed',
    progress: 100,
    recordCount: 2856,
    fileSize: '2.3MB',
    createTime: new Date().toISOString(),
    downloadUrl: '/exports/universities_2023.xlsx'
  },
  {
    id: 2,
    name: '专业录取数据导出',
    dataType: 'majors',
    format: 'csv',
    status: 'processing',
    progress: 65,
    recordCount: 0,
    fileSize: '-',
    createTime: new Date().toISOString()
  }
])

// 导出表单
const exportForm = reactive({
  name: '',
  dataType: 'universities',
  format: 'xlsx',
  filters: {
    years: [],
    provinces: [],
    scoreRange: [400, 650],
    rankStart: null,
    rankEnd: null
  },
  fields: [],
  options: {
    includeHeaders: true,
    compressFile: false,
    splitLargeFiles: false
  }
})

// 可选数据
const availableYears = [2020, 2021, 2022, 2023, 2024]
const availableProvinces = [
  { code: 'BJ', name: '北京' },
  { code: 'SH', name: '上海' },
  { code: 'GD', name: '广东' },
  { code: 'JS', name: '江苏' },
  { code: 'ZJ', name: '浙江' }
]

const availableFields = {
  universities: [
    { label: '学校名称', value: 'name' },
    { label: '学校代码', value: 'code' },
    { label: '所在省份', value: 'province' },
    { label: '学校类型', value: 'type' },
    { label: '办学层次', value: 'level' },
    { label: '录取批次', value: 'batch' },
    { label: '最低分数', value: 'min_score' },
    { label: '最高分数', value: 'max_score' },
    { label: '平均分数', value: 'avg_score' }
  ],
  majors: [
    { label: '专业名称', value: 'name' },
    { label: '专业代码', value: 'code' },
    { label: '学科门类', value: 'category' },
    { label: '学制', value: 'duration' },
    { label: '学位类型', value: 'degree_type' }
  ],
  admissions: [
    { label: '年份', value: 'year' },
    { label: '省份', value: 'province' },
    { label: '科类', value: 'science_type' },
    { label: '批次', value: 'batch' },
    { label: '录取分数', value: 'score' },
    { label: '录取排名', value: 'rank' }
  ],
  statistics: [
    { label: '统计项目', value: 'item' },
    { label: '统计值', value: 'value' },
    { label: '统计时间', value: 'time' }
  ]
}

// 预览数据
const previewData = ref([])

// 表单验证规则
const exportRules = {
  name: [
    { required: true, message: '请输入任务名称', trigger: 'blur' }
  ],
  dataType: [
    { required: true, message: '请选择数据类型', trigger: 'change' }
  ],
  format: [
    { required: true, message: '请选择导出格式', trigger: 'change' }
  ]
}

// 计算属性
const selectedFields = computed(() => {
  return availableFields[exportForm.dataType].filter(field => 
    exportForm.fields.includes(field.value)
  )
})

const estimatedRecords = computed(() => {
  // 根据筛选条件估算记录数
  let base = 10000
  if (exportForm.filters.years.length > 0) {
    base = base * exportForm.filters.years.length / 5
  }
  if (exportForm.filters.provinces.length > 0) {
    base = base * exportForm.filters.provinces.length / 34
  }
  return Math.floor(base)
})

const estimatedFileSize = computed(() => {
  const records = estimatedRecords.value
  const fields = exportForm.fields.length || 5
  let sizeKB = records * fields * 10 // 估算每个字段10字节
  
  if (sizeKB < 1024) {
    return `${sizeKB}KB`
  } else if (sizeKB < 1024 * 1024) {
    return `${(sizeKB / 1024).toFixed(1)}MB`
  } else {
    return `${(sizeKB / 1024 / 1024).toFixed(1)}GB`
  }
})

// 方法
const getDataTypeColor = (type: string) => {
  const colors = {
    universities: 'primary',
    majors: 'success',
    admissions: 'warning',
    statistics: 'info'
  }
  return colors[type] || 'info'
}

const getDataTypeText = (type: string) => {
  const texts = {
    universities: '大学',
    majors: '专业',
    admissions: '录取',
    statistics: '统计'
  }
  return texts[type] || type
}

const getStatusColor = (status: string) => {
  const colors = {
    processing: 'warning',
    completed: 'success',
    failed: 'danger',
    cancelled: 'info'
  }
  return colors[status] || 'info'
}

const getStatusText = (status: string) => {
  const texts = {
    processing: '处理中',
    completed: '已完成',
    failed: '失败',
    cancelled: '已取消'
  }
  return texts[status] || status
}

const formatTime = (timeStr: string) => {
  return new Date(timeStr).toLocaleString()
}

const resetExportForm = () => {
  exportForm.name = ''
  exportForm.dataType = 'universities'
  exportForm.format = 'xlsx'
  exportForm.filters = {
    years: [],
    provinces: [],
    scoreRange: [400, 650],
    rankStart: null,
    rankEnd: null
  }
  exportForm.fields = []
  exportForm.options = {
    includeHeaders: true,
    compressFile: false,
    splitLargeFiles: false
  }
}

const previewExport = async () => {
  try {
    // 模拟预览数据
    previewData.value = [
      { name: '北京大学', code: '10001', province: '北京', type: '综合', min_score: 680 },
      { name: '清华大学', code: '10003', province: '北京', type: '理工', min_score: 685 },
      { name: '复旦大学', code: '10246', province: '上海', type: '综合', min_score: 675 }
    ]
    showPreviewDialog.value = true
  } catch (error) {
    ElMessage.error('预览失败')
  }
}

const startExport = async () => {
  try {
    if (!exportFormRef.value) return
    
    const valid = await exportFormRef.value.validate()
    if (!valid) return
    
    exporting.value = true
    
    // 模拟导出过程
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // 添加到任务列表
    const newTask = {
      id: Date.now(),
      name: exportForm.name,
      dataType: exportForm.dataType,
      format: exportForm.format,
      status: 'processing',
      progress: 0,
      recordCount: 0,
      fileSize: '-',
      createTime: new Date().toISOString()
    }
    
    exportTasks.push(newTask)
    
    ElMessage.success('导出任务已创建')
    showExportDialog.value = false
    
    // 模拟进度更新
    const progressInterval = setInterval(() => {
      newTask.progress += 10
      if (newTask.progress >= 100) {
        newTask.status = 'completed'
        newTask.recordCount = estimatedRecords.value
        newTask.fileSize = estimatedFileSize.value
        clearInterval(progressInterval)
      }
    }, 500)
    
  } catch (error) {
    ElMessage.error('创建导出任务失败')
  } finally {
    exporting.value = false
  }
}

const downloadFile = (task: any) => {
  ElMessage.success(`开始下载 ${task.name}`)
  // 实际实现中这里会触发文件下载
}

const cancelExport = async (task: any) => {
  try {
    await ElMessageBox.confirm('确定要取消此导出任务吗？', '确认取消')
    task.status = 'cancelled'
    ElMessage.success('导出任务已取消')
  } catch (error) {
    // 用户取消操作
  }
}

const retryExport = (task: any) => {
  task.status = 'processing'
  task.progress = 0
  ElMessage.info('正在重新开始导出任务')
}

const deleteExport = async (task: any) => {
  try {
    await ElMessageBox.confirm('确定要删除此导出任务吗？', '确认删除')
    const index = exportTasks.findIndex(t => t.id === task.id)
    if (index > -1) {
      exportTasks.splice(index, 1)
      ElMessage.success('导出任务已删除')
    }
  } catch (error) {
    // 用户取消操作
  }
}

// 组件挂载时初始化默认字段
onMounted(() => {
  exportForm.fields = availableFields[exportForm.dataType].slice(0, 5).map(f => f.value)
})

// 监听数据类型变化，更新默认字段
const handleDataTypeChange = () => {
  exportForm.fields = availableFields[exportForm.dataType].slice(0, 5).map(f => f.value)
}

// 暴露方法给父组件
defineExpose({
  showExportDialog
})
</script>

<style scoped>
.data-export-manager {
  padding: 20px;
}

.export-card {
  border-radius: 12px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.export-tasks {
  margin-top: 16px;
}

.preview-content {
  max-height: 500px;
  overflow-y: auto;
}

.preview-footer {
  margin-top: 16px;
  text-align: center;
}
</style>
