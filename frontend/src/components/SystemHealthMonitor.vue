<template>
  <div class="system-health-monitor">
    <el-card shadow="never" class="health-card">
      <template #header>
        <div class="card-header">
          <h3>系统健康监控</h3>
          <div class="header-actions">
            <el-switch 
              v-model="autoRefresh" 
              active-text="自动刷新" 
              @change="handleAutoRefreshChange"
            />
            <el-button @click="refreshHealth" :loading="loading" size="small">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
      </template>

      <!-- 整体健康状态 -->
      <div class="overall-status">
        <div class="status-indicator" :class="overallStatus.class">
          <el-icon class="status-icon">
            <component :is="overallStatus.icon" />
          </el-icon>
          <div class="status-info">
            <div class="status-text">{{ overallStatus.text }}</div>
            <div class="status-description">{{ overallStatus.description }}</div>
          </div>
        </div>
      </div>

      <!-- 服务状态详情 -->
      <div class="services-status">
        <el-row :gutter="16">
          <el-col :span="8" v-for="service in services" :key="service.name">
            <el-card shadow="hover" class="service-card" :class="service.status">
              <div class="service-content">
                <div class="service-icon">
                  <el-icon>
                    <component :is="service.icon" />
                  </el-icon>
                </div>
                <div class="service-info">
                  <div class="service-name">{{ service.name }}</div>
                  <div class="service-status">{{ service.statusText }}</div>
                  <div class="service-metrics" v-if="service.metrics">
                    <div v-for="(value, key) in service.metrics" :key="key" class="metric">
                      <span class="metric-label">{{ key }}:</span>
                      <span class="metric-value">{{ value }}</span>
                    </div>
                  </div>
                </div>
              </div>
              <div class="service-actions">
                <el-button 
                  v-if="service.actions?.restart" 
                  @click="restartService(service.name)"
                  size="small"
                  type="warning"
                >
                  重启
                </el-button>
                <el-button 
                  v-if="service.actions?.logs" 
                  @click="viewServiceLogs(service.name)"
                  size="small"
                  type="info"
                >
                  日志
                </el-button>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 系统资源监控 -->
      <div class="resource-monitoring">
        <h4>系统资源</h4>
        <el-row :gutter="16">
          <el-col :span="6" v-for="resource in resources" :key="resource.name">
            <div class="resource-item">
              <div class="resource-header">
                <span class="resource-name">{{ resource.name }}</span>
                <span class="resource-value">{{ resource.value }}%</span>
              </div>
              <el-progress 
                :percentage="resource.value" 
                :color="getProgressColor(resource.value)"
                :stroke-width="8"
              />
              <div class="resource-details" v-if="resource.details">
                <div v-for="(value, key) in resource.details" :key="key" class="detail-item">
                  <span>{{ key }}: {{ value }}</span>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 网络连接状态 -->
      <div class="network-status">
        <h4>网络连接</h4>
        <el-table :data="connections" size="small">
          <el-table-column prop="name" label="连接名称" width="150" />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="row.status === 'connected' ? 'success' : 'danger'" size="small">
                {{ row.status === 'connected' ? '已连接' : '断开' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="latency" label="延迟" width="80" />
          <el-table-column prop="lastCheck" label="最后检查" width="160" />
          <el-table-column label="操作" width="120">
            <template #default="{ row }">
              <el-button @click="testConnection(row.name)" size="small" type="primary">
                测试
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 告警信息 -->
      <div class="alerts" v-if="alerts.length > 0">
        <h4>系统告警</h4>
        <div class="alert-list">
          <el-alert
            v-for="alert in alerts"
            :key="alert.id"
            :title="alert.title"
            :description="alert.description"
            :type="alert.type"
            :closable="true"
            @close="dismissAlert(alert.id)"
            style="margin-bottom: 8px"
          />
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import {
  Refresh,
  CircleCheck,
  Warning,
  CircleClose,
  Monitor,
  Connection,
  DataBoard
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

// 响应式数据
const loading = ref(false)
const autoRefresh = ref(true)
const refreshTimer = ref<number | null>(null)

// 系统健康数据
const healthData = reactive({
  backend: { status: 'healthy', responseTime: 45 },
  database: { status: 'healthy', connections: 5 },
  websocket: { status: 'healthy', connections: 3 },
  cache: { status: 'healthy', hitRate: 95.2 }
})

// 系统资源数据
const resources = reactive([
  {
    name: 'CPU使用率',
    value: 35,
    details: { '核心数': '8', '频率': '2.4GHz' }
  },
  {
    name: '内存使用率',
    value: 68,
    details: { '总内存': '16GB', '可用': '5.1GB' }
  },
  {
    name: '磁盘使用率',
    value: 42,
    details: { '总空间': '500GB', '可用': '290GB' }
  },
  {
    name: '网络带宽',
    value: 15,
    details: { '上行': '2.5MB/s', '下行': '15.2MB/s' }
  }
])

// 网络连接状态
const connections = reactive([
  {
    name: 'API服务器',
    status: 'connected',
    latency: '45ms',
    lastCheck: new Date().toLocaleTimeString()
  },
  {
    name: 'WebSocket',
    status: 'connected',
    latency: '32ms',
    lastCheck: new Date().toLocaleTimeString()
  },
  {
    name: '数据库',
    status: 'connected',
    latency: '12ms',
    lastCheck: new Date().toLocaleTimeString()
  }
])

// 告警信息
const alerts = reactive([
  {
    id: 1,
    title: '内存使用率较高',
    description: '当前内存使用率为68%，建议关注',
    type: 'warning'
  }
])

// 计算属性
const services = computed(() => [
  {
    name: '后端服务',
    status: healthData.backend.status === 'healthy' ? 'healthy' : 'error',
    statusText: healthData.backend.status === 'healthy' ? '运行正常' : '服务异常',
    icon: Monitor,
    metrics: {
      '响应时间': `${healthData.backend.responseTime}ms`,
      '状态': healthData.backend.status === 'healthy' ? '正常' : '异常'
    },
    actions: { restart: true, logs: true }
  },
  {
    name: '数据库',
    status: healthData.database.status === 'healthy' ? 'healthy' : 'error',
    statusText: healthData.database.status === 'healthy' ? '连接正常' : '连接异常',
    icon: DataBase,
    metrics: {
      '连接数': `${healthData.database.connections}`,
      '状态': healthData.database.status === 'healthy' ? '正常' : '异常'
    },
    actions: { logs: true }
  },
  {
    name: 'WebSocket',
    status: healthData.websocket.status === 'healthy' ? 'healthy' : 'error',
    statusText: healthData.websocket.status === 'healthy' ? '连接正常' : '连接异常',
    icon: Connection,
    metrics: {
      '活跃连接': `${healthData.websocket.connections}`,
      '状态': healthData.websocket.status === 'healthy' ? '正常' : '异常'
    },
    actions: { restart: true, logs: true }
  }
])

const overallStatus = computed(() => {
  const allHealthy = services.value.every(s => s.status === 'healthy')
  const hasError = services.value.some(s => s.status === 'error')
  
  if (allHealthy) {
    return {
      class: 'healthy',
      icon: CircleCheck,
      text: '系统运行正常',
      description: '所有服务状态良好'
    }
  } else if (hasError) {
    return {
      class: 'error',
      icon: CircleClose,
      text: '系统异常',
      description: '部分服务存在问题'
    }
  } else {
    return {
      class: 'warning',
      icon: Warning,
      text: '系统警告',
      description: '部分服务需要关注'
    }
  }
})

// 方法
const refreshHealth = async () => {
  try {
    loading.value = true
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 更新健康数据
    healthData.backend.responseTime = Math.floor(Math.random() * 100) + 20
    healthData.database.connections = Math.floor(Math.random() * 10) + 1
    healthData.websocket.connections = Math.floor(Math.random() * 5) + 1
    
    // 更新资源数据
    resources.forEach(resource => {
      resource.value = Math.floor(Math.random() * 100)
    })
    
    // 更新连接状态
    connections.forEach(conn => {
      conn.lastCheck = new Date().toLocaleTimeString()
      conn.latency = `${Math.floor(Math.random() * 100) + 10}ms`
    })
    
    ElMessage.success('健康状态已更新')
  } catch (error) {
    console.error('刷新健康状态失败:', error)
    ElMessage.error('刷新健康状态失败')
  } finally {
    loading.value = false
  }
}

const handleAutoRefreshChange = (value: boolean) => {
  if (value) {
    startAutoRefresh()
  } else {
    stopAutoRefresh()
  }
}

const startAutoRefresh = () => {
  if (refreshTimer.value) return
  
  refreshTimer.value = window.setInterval(() => {
    refreshHealth()
  }, 30000) // 30秒刷新一次
}

const stopAutoRefresh = () => {
  if (refreshTimer.value) {
    clearInterval(refreshTimer.value)
    refreshTimer.value = null
  }
}

const getProgressColor = (value: number) => {
  if (value < 50) return '#67c23a'
  if (value < 80) return '#e6a23c'
  return '#f56c6c'
}

const restartService = async (serviceName: string) => {
  try {
    ElMessage.info(`正在重启 ${serviceName}...`)
    // 模拟重启操作
    await new Promise(resolve => setTimeout(resolve, 2000))
    ElMessage.success(`${serviceName} 重启成功`)
    await refreshHealth()
  } catch (error) {
    ElMessage.error(`${serviceName} 重启失败`)
  }
}

const viewServiceLogs = (serviceName: string) => {
  ElMessage.info(`查看 ${serviceName} 日志功能开发中...`)
}

const testConnection = async (connectionName: string) => {
  try {
    const conn = connections.find(c => c.name === connectionName)
    if (conn) {
      ElMessage.info(`正在测试 ${connectionName} 连接...`)
      // 模拟连接测试
      await new Promise(resolve => setTimeout(resolve, 1000))
      conn.lastCheck = new Date().toLocaleTimeString()
      conn.latency = `${Math.floor(Math.random() * 100) + 10}ms`
      ElMessage.success(`${connectionName} 连接测试成功`)
    }
  } catch (error) {
    ElMessage.error(`${connectionName} 连接测试失败`)
  }
}

const dismissAlert = (alertId: number) => {
  const index = alerts.findIndex(alert => alert.id === alertId)
  if (index > -1) {
    alerts.splice(index, 1)
  }
}

// 组件挂载时初始化
onMounted(() => {
  refreshHealth()
  if (autoRefresh.value) {
    startAutoRefresh()
  }
})

onUnmounted(() => {
  stopAutoRefresh()
})

// 暴露方法给父组件
defineExpose({
  refreshHealth,
  startAutoRefresh,
  stopAutoRefresh
})
</script>

<style scoped>
.system-health-monitor {
  padding: 20px;
}

.health-card {
  border-radius: 12px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.overall-status {
  margin-bottom: 24px;
  padding: 20px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 12px;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 16px;
}

.status-indicator.healthy .status-icon {
  color: #67c23a;
  font-size: 32px;
}

.status-indicator.warning .status-icon {
  color: #e6a23c;
  font-size: 32px;
}

.status-indicator.error .status-icon {
  color: #f56c6c;
  font-size: 32px;
}

.status-text {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 4px;
}

.status-description {
  color: #606266;
  font-size: 14px;
}

.services-status {
  margin-bottom: 24px;
}

.service-card {
  border-radius: 8px;
  transition: all 0.3s ease;
}

.service-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.service-card.healthy {
  border-left: 4px solid #67c23a;
}

.service-card.warning {
  border-left: 4px solid #e6a23c;
}

.service-card.error {
  border-left: 4px solid #f56c6c;
}

.service-content {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 12px;
}

.service-icon {
  font-size: 24px;
  color: #409eff;
}

.service-info {
  flex: 1;
}

.service-name {
  font-weight: 600;
  margin-bottom: 4px;
}

.service-status {
  color: #606266;
  font-size: 14px;
  margin-bottom: 8px;
}

.service-metrics {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.metric {
  font-size: 12px;
  color: #909399;
}

.metric-label {
  margin-right: 4px;
}

.metric-value {
  font-weight: 500;
}

.service-actions {
  display: flex;
  gap: 8px;
}

.resource-monitoring {
  margin-bottom: 24px;
}

.resource-monitoring h4 {
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: 600;
}

.resource-item {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.resource-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.resource-name {
  font-weight: 500;
}

.resource-value {
  font-weight: 600;
  color: #409eff;
}

.resource-details {
  margin-top: 8px;
  font-size: 12px;
  color: #909399;
}

.detail-item {
  margin-bottom: 2px;
}

.network-status {
  margin-bottom: 24px;
}

.network-status h4 {
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: 600;
}

.alerts h4 {
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: 600;
}

.alert-list {
  max-height: 300px;
  overflow-y: auto;
}
</style>
