<template>
  <div class="crawler-schedule-manager">
    <el-card shadow="never" class="schedule-card">
      <template #header>
        <div class="card-header">
          <h3>爬虫调度管理</h3>
          <div class="header-actions">
            <el-button @click="refreshSchedules" :loading="loading">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
            <el-button @click="showCreateDialog = true" type="primary">
              <el-icon><Plus /></el-icon>
              新建调度
            </el-button>
          </div>
        </div>
      </template>

      <!-- 调度统计 -->
      <div class="schedule-stats">
        <el-row :gutter="16">
          <el-col :span="6">
            <el-card shadow="never" class="stat-card">
              <el-statistic title="总调度数" :value="scheduleStats.total" />
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card shadow="never" class="stat-card">
              <el-statistic title="活跃调度" :value="scheduleStats.active" />
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card shadow="never" class="stat-card">
              <el-statistic title="今日执行" :value="scheduleStats.todayExecuted" />
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card shadow="never" class="stat-card">
              <el-statistic title="下次执行" :value="scheduleStats.nextExecution" />
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 调度列表 -->
      <div class="schedule-list">
        <div class="list-header">
          <h4>调度列表</h4>
          <div class="list-filters">
            <el-space>
              <el-select v-model="filterStatus" placeholder="状态" size="small" style="width: 120px">
                <el-option label="全部" value="" />
                <el-option label="活跃" value="active" />
                <el-option label="暂停" value="paused" />
                <el-option label="禁用" value="disabled" />
              </el-select>
              
              <el-select v-model="filterType" placeholder="类型" size="small" style="width: 120px">
                <el-option label="全部" value="" />
                <el-option label="大学数据" value="university" />
                <el-option label="专业数据" value="major" />
              </el-select>
            </el-space>
          </div>
        </div>

        <el-table 
          :data="filteredSchedules" 
          v-loading="loading"
          stripe
          @row-click="selectSchedule"
        >
          <el-table-column prop="name" label="调度名称" min-width="150" />
          <el-table-column prop="data_type" label="数据类型" width="100">
            <template #default="{ row }">
              <el-tag size="small" :type="row.data_type === 'university' ? 'primary' : 'success'">
                {{ row.data_type === 'university' ? '大学' : '专业' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="schedule_type" label="执行频率" width="100">
            <template #default="{ row }">
              {{ getScheduleTypeText(row.schedule_type) }}
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag size="small" :type="getStatusType(row.status)">
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="next_run" label="下次执行" width="160">
            <template #default="{ row }">
              {{ row.next_run ? formatTime(row.next_run) : '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="last_run" label="上次执行" width="160">
            <template #default="{ row }">
              {{ row.last_run ? formatTime(row.last_run) : '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="execution_count" label="执行次数" width="100" />
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <el-button-group size="small">
                <el-button @click.stop="editSchedule(row)" title="编辑">
                  <el-icon><Edit /></el-icon>
                </el-button>
                <el-button
                  v-if="row.status === 'active'"
                  @click.stop="pauseSchedule(row)"
                  type="warning"
                  title="暂停"
                >
                  <el-icon><VideoPause /></el-icon>
                </el-button>
                <el-button
                  v-if="row.status === 'paused'"
                  @click.stop="resumeSchedule(row)"
                  type="success"
                  title="恢复"
                >
                  <el-icon><VideoPlay /></el-icon>
                </el-button>
                <el-button
                  @click.stop="executeNow(row)"
                  type="primary"
                  title="立即执行"
                >
                  <el-icon><CaretRight /></el-icon>
                </el-button>
                <el-button @click.stop="deleteSchedule(row)" type="danger" title="删除">
                  <el-icon><Delete /></el-icon>
                </el-button>
              </el-button-group>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>

    <!-- 创建/编辑调度对话框 -->
    <el-dialog 
      v-model="showCreateDialog" 
      :title="editingSchedule ? '编辑调度' : '新建调度'"
      width="600px"
      @close="resetForm"
    >
      <el-form :model="scheduleForm" :rules="scheduleRules" ref="scheduleFormRef" label-width="120px">
        <el-form-item label="调度名称" prop="name">
          <el-input v-model="scheduleForm.name" placeholder="请输入调度名称" />
        </el-form-item>
        
        <el-form-item label="数据类型" prop="data_type">
          <el-radio-group v-model="scheduleForm.data_type">
            <el-radio value="university">大学数据</el-radio>
            <el-radio value="major">专业数据</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="执行频率" prop="schedule_type">
          <el-select v-model="scheduleForm.schedule_type" style="width: 100%">
            <el-option label="每日执行" value="daily" />
            <el-option label="每周执行" value="weekly" />
            <el-option label="每月执行" value="monthly" />
            <el-option label="自定义" value="custom" />
          </el-select>
        </el-form-item>

        <el-form-item v-if="scheduleForm.schedule_type === 'daily'" label="执行时间" prop="schedule_time">
          <el-time-picker 
            v-model="scheduleForm.schedule_time" 
            format="HH:mm"
            placeholder="选择执行时间"
          />
        </el-form-item>

        <el-form-item v-if="scheduleForm.schedule_type === 'weekly'" label="执行日期" prop="schedule_weekday">
          <el-select v-model="scheduleForm.schedule_weekday" placeholder="选择星期几">
            <el-option label="星期一" :value="1" />
            <el-option label="星期二" :value="2" />
            <el-option label="星期三" :value="3" />
            <el-option label="星期四" :value="4" />
            <el-option label="星期五" :value="5" />
            <el-option label="星期六" :value="6" />
            <el-option label="星期日" :value="0" />
          </el-select>
        </el-form-item>

        <el-form-item v-if="scheduleForm.schedule_type === 'custom'" label="Cron表达式" prop="cron_expression">
          <el-input 
            v-model="scheduleForm.cron_expression" 
            placeholder="0 2 * * *"
          />
          <el-text type="info" size="small">
            格式：分 时 日 月 周，例如 "0 2 * * *" 表示每天凌晨2点执行
          </el-text>
        </el-form-item>

        <el-form-item label="爬取参数">
          <el-row :gutter="12">
            <el-col :span="12">
              <el-select v-model="scheduleForm.years" multiple placeholder="年份" style="width: 100%">
                <el-option v-for="year in availableYears" :key="year" :label="year" :value="year" />
              </el-select>
            </el-col>
            <el-col :span="12">
              <el-select v-model="scheduleForm.provinces" multiple placeholder="省份" style="width: 100%">
                <el-option v-for="province in availableProvinces" :key="province.code" :label="province.name" :value="province.code" />
              </el-select>
            </el-col>
          </el-row>
        </el-form-item>

        <el-form-item label="启用状态">
          <el-switch v-model="scheduleForm.enabled" />
        </el-form-item>

        <el-form-item label="描述">
          <el-input 
            v-model="scheduleForm.description" 
            type="textarea" 
            :rows="3"
            placeholder="可选，描述此调度的用途"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button @click="saveSchedule" type="primary" :loading="saving">
          {{ editingSchedule ? '更新' : '创建' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { 
  Refresh, 
  Plus, 
  Edit, 
  Delete, 
  VideoPause, 
  VideoPlay, 
  CaretRight 
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance } from 'element-plus'

// 响应式数据
const loading = ref(false)
const saving = ref(false)
const showCreateDialog = ref(false)
const editingSchedule = ref<any>(null)
const scheduleFormRef = ref<FormInstance>()

// 过滤条件
const filterStatus = ref('')
const filterType = ref('')

// 调度数据
const schedules = ref<any[]>([])
const scheduleStats = ref({
  total: 0,
  active: 0,
  todayExecuted: 0,
  nextExecution: '-'
})

// 表单数据
const scheduleForm = ref({
  name: '',
  data_type: 'university',
  schedule_type: 'daily',
  schedule_time: null,
  schedule_weekday: 1,
  cron_expression: '0 2 * * *',
  years: [],
  provinces: [],
  enabled: true,
  description: ''
})

// 可选数据
const availableYears = ref([2020, 2021, 2022, 2023, 2024])
const availableProvinces = ref([
  { code: 'BJ', name: '北京' },
  { code: 'SH', name: '上海' },
  { code: 'GD', name: '广东' },
  // ... 更多省份
])

// 表单验证规则
const scheduleRules = {
  name: [
    { required: true, message: '请输入调度名称', trigger: 'blur' }
  ],
  data_type: [
    { required: true, message: '请选择数据类型', trigger: 'change' }
  ],
  schedule_type: [
    { required: true, message: '请选择执行频率', trigger: 'change' }
  ]
}

// 计算属性
const filteredSchedules = computed(() => {
  let result = schedules.value
  
  if (filterStatus.value) {
    result = result.filter(schedule => schedule.status === filterStatus.value)
  }
  
  if (filterType.value) {
    result = result.filter(schedule => schedule.data_type === filterType.value)
  }
  
  return result
})

// 方法
const refreshSchedules = async () => {
  try {
    loading.value = true
    // 这里调用API获取调度列表
    // const response = await crawlerApi.getSchedules()
    // schedules.value = response.data
    
    // 模拟数据
    schedules.value = [
      {
        id: 1,
        name: '每日大学数据更新',
        data_type: 'university',
        schedule_type: 'daily',
        status: 'active',
        next_run: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
        last_run: new Date().toISOString(),
        execution_count: 15
      }
    ]
    
    updateStats()
  } catch (error) {
    console.error('获取调度列表失败:', error)
    ElMessage.error('获取调度列表失败')
  } finally {
    loading.value = false
  }
}

const updateStats = () => {
  scheduleStats.value = {
    total: schedules.value.length,
    active: schedules.value.filter(s => s.status === 'active').length,
    todayExecuted: schedules.value.filter(s => {
      if (!s.last_run) return false
      const lastRun = new Date(s.last_run)
      const today = new Date()
      return lastRun.toDateString() === today.toDateString()
    }).length,
    nextExecution: getNextExecution()
  }
}

const getNextExecution = () => {
  const activeSchedules = schedules.value.filter(s => s.status === 'active' && s.next_run)
  if (activeSchedules.length === 0) return '-'
  
  const nextRuns = activeSchedules.map(s => new Date(s.next_run))
  const earliest = new Date(Math.min(...nextRuns.map(d => d.getTime())))
  
  return formatTime(earliest.toISOString())
}

const selectSchedule = (schedule: any) => {
  // 选择调度逻辑
}

const editSchedule = (schedule: any) => {
  editingSchedule.value = schedule
  Object.assign(scheduleForm.value, schedule)
  showCreateDialog.value = true
}

const saveSchedule = async () => {
  try {
    if (!scheduleFormRef.value) return
    
    const valid = await scheduleFormRef.value.validate()
    if (!valid) return
    
    saving.value = true
    
    // 这里调用API保存调度
    // if (editingSchedule.value) {
    //   await crawlerApi.updateSchedule(editingSchedule.value.id, scheduleForm.value)
    // } else {
    //   await crawlerApi.createSchedule(scheduleForm.value)
    // }
    
    ElMessage.success(editingSchedule.value ? '调度更新成功' : '调度创建成功')
    showCreateDialog.value = false
    await refreshSchedules()
  } catch (error) {
    console.error('保存调度失败:', error)
    ElMessage.error('保存调度失败')
  } finally {
    saving.value = false
  }
}

const pauseSchedule = async (schedule: any) => {
  try {
    // await crawlerApi.pauseSchedule(schedule.id)
    ElMessage.success('调度已暂停')
    await refreshSchedules()
  } catch (error) {
    console.error('暂停调度失败:', error)
    ElMessage.error('暂停调度失败')
  }
}

const resumeSchedule = async (schedule: any) => {
  try {
    // await crawlerApi.resumeSchedule(schedule.id)
    ElMessage.success('调度已恢复')
    await refreshSchedules()
  } catch (error) {
    console.error('恢复调度失败:', error)
    ElMessage.error('恢复调度失败')
  }
}

const executeNow = async (schedule: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要立即执行调度 "${schedule.name}" 吗？`,
      '确认执行',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }
    )
    
    // await crawlerApi.executeScheduleNow(schedule.id)
    ElMessage.success('调度已开始执行')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('执行调度失败:', error)
      ElMessage.error('执行调度失败')
    }
  }
}

const deleteSchedule = async (schedule: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除调度 "${schedule.name}" 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // await crawlerApi.deleteSchedule(schedule.id)
    ElMessage.success('调度已删除')
    await refreshSchedules()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除调度失败:', error)
      ElMessage.error('删除调度失败')
    }
  }
}

const resetForm = () => {
  editingSchedule.value = null
  scheduleForm.value = {
    name: '',
    data_type: 'university',
    schedule_type: 'daily',
    schedule_time: null,
    schedule_weekday: 1,
    cron_expression: '0 2 * * *',
    years: [],
    provinces: [],
    enabled: true,
    description: ''
  }
}

const getScheduleTypeText = (type: string) => {
  const types: Record<string, string> = {
    daily: '每日',
    weekly: '每周',
    monthly: '每月',
    custom: '自定义'
  }
  return types[type] || type
}

const getStatusType = (status: string) => {
  const types: Record<string, string> = {
    active: 'success',
    paused: 'warning',
    disabled: 'info'
  }
  return types[status] || 'info'
}

const getStatusText = (status: string) => {
  const texts: Record<string, string> = {
    active: '活跃',
    paused: '暂停',
    disabled: '禁用'
  }
  return texts[status] || status
}

const formatTime = (timeStr: string) => {
  return new Date(timeStr).toLocaleString()
}

// 组件挂载时加载数据
onMounted(() => {
  refreshSchedules()
})

// 暴露方法给父组件
defineExpose({
  refreshSchedules,
  showCreateDialog
})
</script>

<style scoped>
.crawler-schedule-manager {
  padding: 20px;
}

.schedule-card {
  border-radius: 8px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 500;
}

.schedule-stats {
  margin-bottom: 24px;
}

.stat-card {
  text-align: center;
}

.schedule-list h4 {
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: 500;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}
</style>
