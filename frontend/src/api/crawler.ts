/**
 * 爬虫相关API
 */

import { request } from './request'

export interface CrawlTaskRequest {
  years?: number[]
  provinces?: string[]
  science_types?: string[]
  batch_types?: string[]
  university_codes?: string[]
}

export interface CrawlTaskResponse {
  task_id: string
  message: string
  status: string
}

export interface TaskStatusResponse {
  task_id: string
  task_name: string
  data_type: string
  status: string
  start_time?: string
  end_time?: string
  duration?: number
  total_requests?: number
  success_requests?: number
  failed_requests?: number
  total_records?: number
  new_records?: number
  updated_records?: number
  error_message?: string
  success_rate?: number
}

export interface TaskListResponse {
  data: TaskStatusResponse[]
  total: number
  page: number
  size: number
  pages: number
}

export interface CrawlerConfigResponse {
  api_endpoints: {
    university: string
    major: string
  }
  target_years: number[]
  provinces: string[]
  science_types: Record<string, string>
  batch_types: Record<string, string>
  crawler_settings: {
    max_concurrent_requests: number
    request_delay: number
    retry_times: number
    timeout: number
    batch_size: number
  }
}

export const crawlerApi = {
  /**
   * 获取爬虫配置
   */
  async getConfig(): Promise<CrawlerConfigResponse> {
    return request.get('/crawler/config')
  },

  /**
   * 更新爬虫配置
   */
  async updateConfig(config: any): Promise<{ success: boolean; message: string; data: any }> {
    return request.post('/crawler/config', config)
  },

  /**
   * 启动大学数据爬取
   */
  async startUniversityCrawl(params: CrawlTaskRequest): Promise<CrawlTaskResponse> {
    return request.post('/crawler/universities/start', params)
  },

  /**
   * 启动专业数据爬取
   */
  async startMajorCrawl(params: CrawlTaskRequest): Promise<CrawlTaskResponse> {
    return request.post('/crawler/majors/start', params)
  },

  /**
   * 获取任务状态
   */
  async getTaskStatus(taskId: string): Promise<TaskStatusResponse> {
    return request.get(`/crawler/tasks/${taskId}`)
  },

  /**
   * 获取任务列表
   */
  async getTasks(params?: {
    page?: number
    size?: number
    data_type?: string
    status?: string
  }): Promise<TaskListResponse> {
    return request.get('/crawler/tasks', { params })
  },

  /**
   * 执行任务操作
   */
  async executeTaskAction(taskId: string, action: 'pause' | 'resume' | 'cancel'): Promise<{
    success: boolean
    message: string
    task_id: string
    action: string
  }> {
    return request.post(`/ws/tasks/${taskId}/action`, { action })
  },

  /**
   * 获取所有任务状态
   */
  async getAllTasksStatus(): Promise<Record<string, any>> {
    return request.get('/ws/tasks/status')
  },

  /**
   * 获取连接统计
   */
  async getConnectionStats(): Promise<{
    active_connections: number
    task_subscriptions: Record<string, number>
  }> {
    return request.get('/ws/connections/stats')
  },

  /**
   * 删除任务
   */
  async deleteTask(taskId: string): Promise<{ success: boolean; message: string; task_id: string }> {
    return request.delete(`/crawler/tasks/${taskId}`)
  }
}
