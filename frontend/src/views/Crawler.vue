<template>
  <div class="crawler-container">
    <!-- 现代化爬虫管理界面 -->
    <EnhancedCrawlerDashboard />
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { useCrawlerStore } from '../stores/crawler'
import EnhancedCrawlerDashboard from '../components/EnhancedCrawlerDashboard.vue'

const crawlerStore = useCrawlerStore()

// 组件挂载时初始化
onMounted(async () => {
  try {
    await crawlerStore.connectWebSocket()
  } catch (error) {
    console.error('连接WebSocket失败:', error)
  }
})
</script>

<style scoped>
.crawler-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}
</style>
