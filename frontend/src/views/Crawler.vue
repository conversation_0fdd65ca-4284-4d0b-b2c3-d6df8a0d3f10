<template>
  <div class="crawler-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1>高考数据爬虫管理平台</h1>
          <p>智能化数据收集、实时监控和可视化管理中心</p>
        </div>
        <div class="header-right">
          <el-space>
            <el-switch
              v-model="useEnhancedView"
              active-text="增强视图"
              inactive-text="经典视图"
              @change="handleViewChange"
            />
            <el-button @click="viewApiDocs" type="info">
              <el-icon><Document /></el-icon>
              API文档
            </el-button>
          </el-space>
        </div>
      </div>
    </div>

    <!-- 增强视图 -->
    <div v-if="useEnhancedView" class="enhanced-view">
      <EnhancedCrawlerDashboard />
    </div>

    <!-- 经典视图 -->
    <div v-else class="classic-view">
      <el-tabs v-model="activeTab" type="card" class="main-tabs">
        <el-tab-pane label="仪表板" name="dashboard">
          <div class="tab-content">
            <!-- 系统状态 -->
            <SystemStatus />

            <!-- 数据概览 -->
            <DataOverview
              @create-task="showCreateTaskDialog"
              @view-tasks="scrollToTaskManager"
              @view-logs="scrollToLogs"
            />
          </div>
        </el-tab-pane>

        <el-tab-pane label="任务管理" name="tasks">
          <div class="tab-content">
            <CrawlerTaskManager ref="taskManagerRef" />
          </div>
        </el-tab-pane>

        <el-tab-pane label="实时日志" name="logs">
          <div class="tab-content">
            <el-card ref="logsRef" class="log-card" shadow="never">
              <template #header>
                <div class="card-header">
                  <span>实时日志</span>
                  <div class="header-actions">
                    <el-button-group size="small">
                      <el-button @click="connectWebSocket" :disabled="wsConnected" type="success">
                        <el-icon><Connection /></el-icon>
                        {{ wsConnected ? '已连接' : '连接' }}
                      </el-button>
                      <el-button @click="disconnectWebSocket" :disabled="!wsConnected" type="warning">
                        <el-icon><Close /></el-icon>
                        断开
                      </el-button>
                    </el-button-group>
                  </div>
                </div>
              </template>
              <RealTimeLog height="400px" />
            </el-card>
          </div>
        </el-tab-pane>

        <el-tab-pane label="爬虫配置" name="config">
          <div class="tab-content">
            <CrawlerConfigEditor />
          </div>
        </el-tab-pane>

        <el-tab-pane label="调度管理" name="schedule">
          <div class="tab-content">
            <CrawlerScheduleManager />
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Connection,
  Close,
  Document
} from '@element-plus/icons-vue'
import { useCrawlerStore } from '../stores/crawler'
import SystemStatus from '../components/SystemStatus.vue'
import DataOverview from '../components/DataOverview.vue'
import CrawlerTaskManager from '../components/CrawlerTaskManager.vue'
import RealTimeLog from '../components/RealTimeLog.vue'
import EnhancedCrawlerDashboard from '../components/EnhancedCrawlerDashboard.vue'
import CrawlerConfigEditor from '../components/CrawlerConfigEditor.vue'
import CrawlerScheduleManager from '../components/CrawlerScheduleManager.vue'

const crawlerStore = useCrawlerStore()

// 响应式数据
const taskManagerRef = ref()
const logsRef = ref()
const useEnhancedView = ref(true)
const activeTab = ref('dashboard')

// 计算属性
const wsConnected = computed(() => crawlerStore.isWebSocketConnected)

// 显示创建任务对话框
const showCreateTaskDialog = () => {
  // 触发任务管理器的创建任务功能
  if (taskManagerRef.value) {
    // 直接设置响应式变量
    taskManagerRef.value.showCreateDialog = true
  }
}

// 滚动到任务管理器
const scrollToTaskManager = () => {
  if (taskManagerRef.value) {
    taskManagerRef.value.$el.scrollIntoView({ behavior: 'smooth' })
  }
}

// 滚动到日志区域
const scrollToLogs = () => {
  if (logsRef.value) {
    logsRef.value.$el.scrollIntoView({ behavior: 'smooth' })
  }
}

// 连接WebSocket
const connectWebSocket = async () => {
  try {
    await crawlerStore.connectWebSocket()
  } catch (error) {
    console.error('连接WebSocket失败:', error)
  }
}

// 断开WebSocket
const disconnectWebSocket = () => {
  crawlerStore.disconnectWebSocket()
  ElMessage.info('WebSocket连接已断开')
}

// 查看API文档
const viewApiDocs = () => {
  window.open('http://localhost:8000/docs', '_blank')
}

// 处理视图切换
const handleViewChange = (value: boolean) => {
  ElMessage.success(value ? '已切换到增强视图' : '已切换到经典视图')
}

// 初始化
onMounted(async () => {
  await crawlerStore.loadConfig()
  await connectWebSocket()
})
</script>

<style scoped>
.crawler-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.page-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 24px;
  margin-bottom: 24px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1400px;
  margin: 0 auto;
}

.header-left h1 {
  font-size: 32px;
  color: #2c3e50;
  margin-bottom: 8px;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.header-left p {
  color: #606266;
  font-size: 16px;
  margin: 0;
}

.enhanced-view {
  padding: 0;
}

.classic-view {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 24px;
}

.main-tabs {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 16px;
}

.tab-content {
  padding: 16px 0;
}

.status-row {
  margin-bottom: 24px;
}

.status-card {
  margin-bottom: 16px;
}

.status-content {
  display: flex;
  align-items: center;
  padding: 10px;
}

.status-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
  color: white;
}

.status-icon.healthy {
  background: linear-gradient(135deg, #67c23a, #85ce61);
}

.status-icon.database {
  background: linear-gradient(135deg, #409eff, #66b1ff);
}

.status-icon.crawler {
  background: linear-gradient(135deg, #e6a23c, #ebb563);
}

.status-icon.websocket-connected {
  background: linear-gradient(135deg, #67c23a, #85ce61);
}

.status-icon.websocket-disconnected {
  background: linear-gradient(135deg, #f56c6c, #f78989);
}

.status-title {
  font-size: 14px;
  color: #909399;
  margin-bottom: 4px;
}

.status-value {
  font-size: 18px;
  font-weight: bold;
  color: #303133;
}

.overview-card,
.log-card {
  margin-bottom: 24px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.overview-item {
  text-align: center;
  padding: 20px 0;
}

.overview-number {
  font-size: 32px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 8px;
}

.overview-label {
  font-size: 14px;
  color: #606266;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-actions {
    flex-direction: column;
  }

  .header-actions .el-button-group {
    width: 100%;
  }
}
</style>
