# 🎓 高考数据爬虫管理平台 v0.3.0

> 智能化数据收集、实时监控和可视化管理中心

[![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://python.org)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.104+-green.svg)](https://fastapi.tiangolo.com)
[![Vue.js](https://img.shields.io/badge/Vue.js-3.4+-brightgreen.svg)](https://vuejs.org)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.4+-blue.svg)](https://typescriptlang.org)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

## 🚀 快速开始

### 一键启动
```bash
python start_project.py
```

### 手动启动
```bash
# 后端服务
python backend/app/simple_main.py

# 前端服务 (新终端)
cd frontend && npm run dev
```

### 访问地址
- 🌐 **前端界面**: http://localhost:3000
- 📚 **API文档**: http://localhost:8000/docs
- 🔍 **健康检查**: http://localhost:8000/api/health

## ✨ 核心功能

### 🕷️ 智能爬虫管理
- **可视化配置编辑器**: 支持API设置、请求头、代理配置
- **实时任务监控**: WebSocket实时状态更新和日志推送
- **断点续传机制**: 任务中断后可继续执行
- **批量操作支持**: 多任务并发管理

### ⏰ 定时任务调度
- **多种调度模式**: 每日/每周/每月/自定义Cron表达式
- **时间窗口控制**: 避免业务高峰期执行
- **执行历史跟踪**: 完整的调度记录和统计
- **智能冲突检测**: 自动优化调度安排

### 📊 数据分析可视化
- **多维度统计分析**: 大学排名、专业热度、录取趋势
- **交互式图表**: ECharts支持的丰富图表类型
- **实时数据监控**: 系统状态和任务进度实时展示
- **数据导出功能**: 支持图表和数据导出

### 🎨 现代化界面设计
- **双视图模式**: 增强视图和经典视图自由切换
- **响应式布局**: 适配不同设备和屏幕尺寸
- **毛玻璃效果**: 现代化渐变背景和视觉效果
- **智能交互**: 悬停动画、加载状态、操作反馈

## 🏗️ 技术架构

### 后端技术栈
- **Python 3.8+** - 主要开发语言
- **FastAPI 0.104+** - 高性能异步API框架
- **Tortoise ORM** - 异步ORM框架
- **SQLite** - 轻量级数据库
- **WebSocket** - 实时通信
- **httpx** - 异步HTTP客户端
- **Pydantic** - 数据验证和设置管理

### 前端技术栈
- **Vue 3.4+ + TypeScript** - 现代前端框架
- **Element Plus** - 企业级UI组件库
- **ECharts** - 专业数据可视化
- **Pinia** - 新一代状态管理
- **Vue Router** - 官方路由管理
- **Vite** - 极速构建工具

### 核心特性
- **🔄 实时通信**: WebSocket双向通信
- **📱 响应式设计**: 适配多种设备
- **🎯 类型安全**: 全栈TypeScript支持
- **⚡ 高性能**: 异步处理和优化缓存
- **🛡️ 安全可靠**: 输入验证和错误处理
- **🔧 易于部署**: 一键启动和Docker支持

## 🗄️ 数据库设计

### 核心表结构

基于API文档中的数据字段设计，主要包含：

1. **universities** - 大学信息表
   - 对应API中的collage相关字段
   - 包含985/211标识、学校性质等

2. **majors** - 专业信息表
   - 对应API中的major相关字段
   - 包含专业分类、学科信息等

3. **admission_records** - 学校录取记录表
   - 对应第一个API的返回数据
   - 包含录取分数、排名、招生计划等

4. **major_admission_records** - 专业录取记录表
   - 对应第二个API的返回数据
   - 包含专业录取分数、选科要求等

5. **基础数据表** - provinces、batches、science_types等

## 🕷️ 爬虫设计

### API接口
基于提供的API文档：
- **获取学校详细信息**: `POST /prod/history/front/history/ptwlList`
- **获取专业详情数据**: `POST /prod/history/front/history/ptwlMajorList`

### 爬取策略
- **分批次爬取**：按年份、省份、科类分批
  - 年份：2020-2024
  - 省份：全国31个省市自治区
  - 科类：历史类(1)、物理类(5)
  - 批次：本科提前批A段(0)、本科提前批B段(1)、本科批(2)、高职专科批(5)
- **并发控制**：使用asyncio控制并发数量
- **增量更新**：只爬取新增或变更的数据
- **错误处理**：完善的重试和异常处理机制

## 🚀 快速开始

### 环境要求
- Python 3.11+
- PostgreSQL 12+
- Redis 6+
- Node.js 16+

### 后端启动

1. **安装依赖**
```bash
cd backend
pip install -r requirements.txt
```

2. **配置环境变量**
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑配置文件
DATABASE_URL=postgres://user:password@localhost:5432/gaokao_db
REDIS_URL=redis://localhost:6379/0
GAOKAO_API_TOKEN=your_api_token
```

3. **初始化数据库**
```bash
# 初始化数据库迁移
aerich init -t app.core.database.TORTOISE_ORM

# 生成迁移文件
aerich init-db

# 执行迁移
aerich upgrade
```

4. **启动服务**
```bash
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### 前端启动

```bash
cd frontend
npm install
npm run dev
```

## 📊 功能特性

### 数据收集
- ✅ 异步爬虫系统
- ✅ 多维度数据收集（年份、省份、科类、批次）
- ✅ 数据去重和清洗
- ✅ 增量更新机制
- ✅ 爬取日志和监控

### 数据存储
- ✅ 完整的数据库模型设计
- ✅ 基于Tortoise ORM的异步操作
- ✅ 数据完整性约束
- ✅ 索引优化

### API服务
- 🔄 大学查询接口
- 🔄 专业查询接口
- 🔄 录取数据查询接口
- 🔄 统计分析接口
- 🔄 数据导出接口

### 前端功能
- 🔄 首页仪表板
- 🔄 院校查询和筛选
- 🔄 专业分析和对比
- 🔄 数据可视化图表
- 🔄 志愿填报助手

## 🔧 配置说明

### 爬虫配置
```python
# app/crawler/config.py
class CrawlerConfig:
    # API端点
    UNIVERSITY_API_URL = "https://applet.cqzk.com.cn/prod/history/front/history/ptwlList"
    MAJOR_API_URL = "https://applet.cqzk.com.cn/prod/history/front/history/ptwlMajorList"
    
    # 并发控制
    MAX_CONCURRENT_REQUESTS = 5
    REQUEST_DELAY = 1.0
    RETRY_TIMES = 3
```

### 数据库配置
```python
# app/core/database.py
TORTOISE_ORM = {
    "connections": {
        "default": "postgres://user:password@localhost:5432/gaokao_db"
    },
    "apps": {
        "models": {
            "models": ["app.models.*"],
            "default_connection": "default",
        },
    },
}
```

## 📈 数据分析功能

### 统计分析
- 录取难度分析
- 分数线趋势分析
- 专业热度排行
- 地区竞争分析

### 可视化图表
- 趋势图：历年分数线变化
- 散点图：分数与排名关系
- 热力图：地区录取难度
- 柱状图：专业热度排行

## 🔍 API文档

启动后端服务后，访问以下地址查看API文档：
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## 📝 开发计划

### 已完成
- [x] 项目架构设计
- [x] 数据库模型设计
- [x] 爬虫核心功能
- [x] 数据处理模块

### 进行中
- [ ] API接口开发
- [ ] 前端页面开发
- [ ] 数据可视化

### 待开发
- [ ] 用户系统
- [ ] 权限管理
- [ ] 数据导出
- [ ] 移动端适配

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系方式

如有问题或建议，请通过以下方式联系：
- 提交 Issue
- 发送邮件

---

**注意**: 本项目仅用于学习和研究目的，请遵守相关API的使用条款和法律法规。
