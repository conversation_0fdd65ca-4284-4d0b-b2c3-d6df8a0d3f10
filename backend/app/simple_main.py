"""
高考数据收集分析平台 - 简化版FastAPI主应用
"""

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from contextlib import asynccontextmanager
import uvicorn

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.core.simple_database import init_db, close_db, create_initial_data, get_db_stats
from app.api import universities, majors, admissions, crawler, statistics, base_data
from app.api import websocket as websocket_api


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时初始化数据库
    await init_db()
    await create_initial_data()
    print("✅ 数据库初始化完成")
    
    yield
    
    # 关闭时清理资源
    await close_db()
    print("✅ 数据库连接已关闭")


# 创建FastAPI应用
app = FastAPI(
    title="高考数据收集分析平台",
    description="基于API文档设计的高考院校专业数据收集、存储、分析和可视化展示平台",
    version="0.2.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 开发环境允许所有来源
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# 注册API路由
app.include_router(universities.router, prefix="/api/universities", tags=["大学"])
app.include_router(majors.router, prefix="/api/majors", tags=["专业"])
app.include_router(admissions.router, prefix="/api/admissions", tags=["录取数据"])
app.include_router(crawler.router, prefix="/api/crawler", tags=["数据爬取"])
app.include_router(statistics.router, prefix="/api/statistics", tags=["统计分析"])
app.include_router(base_data.router, prefix="/api/base-data", tags=["基础数据"])
app.include_router(websocket_api.router, prefix="/api/ws", tags=["WebSocket"])

# 尝试注册新功能路由
try:
    from app.api import analytics
    app.include_router(analytics.router, prefix="/api/analytics", tags=["数据分析"])
    print("✅ 数据分析API路由注册完成")
except Exception as e:
    print(f"⚠️ 数据分析API路由注册失败: {e}")


@app.get("/", summary="根路径", description="API根路径，返回基本信息")
async def root():
    """根路径"""
    return {
        "message": "高考数据收集分析平台 API",
        "version": "0.2.0",
        "docs": "/docs",
        "redoc": "/redoc",
        "mode": "simplified"
    }


@app.get("/api/health", summary="健康检查", description="检查API服务状态")
async def health_check():
    """健康检查"""
    try:
        # 检查数据库连接
        stats = await get_db_stats()
        return {
            "status": "healthy",
            "database": "connected",
            "mode": "simplified",
            "stats": stats
        }
    except Exception as e:
        return JSONResponse(
            status_code=503,
            content={
                "status": "unhealthy",
                "database": "disconnected",
                "mode": "simplified",
                "error": str(e)
            }
        )


@app.get("/api/info", summary="系统信息", description="获取系统基本信息")
async def system_info():
    """系统信息"""
    from app.crawler.config import CrawlerConfig
    
    config = CrawlerConfig()
    
    return {
        "system": {
            "name": "高考数据收集分析平台",
            "version": "0.2.0",
            "mode": "simplified",
            "database": "SQLite",
            "orm": "Tortoise ORM"
        },
        "crawler": {
            "science_types": config.SCIENCE_TYPES,
            "batch_types": config.BATCH_TYPES,
            "target_years": config.TARGET_YEARS,
            "provinces_count": len(config.TARGET_PROVINCES)
        },
        "database": await get_db_stats(),
        "features": {
            "core_apis": True,
            "crawler_management": True,
            "websocket_support": True,
            "data_analysis": "limited",
            "caching": False,
            "security_middleware": False
        }
    }


# 全局异常处理
@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """全局异常处理"""
    return JSONResponse(
        status_code=500,
        content={
            "error": "Internal Server Error",
            "message": str(exc),
            "path": str(request.url),
            "mode": "simplified"
        }
    )


if __name__ == "__main__":
    uvicorn.run(
        "app.simple_main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
