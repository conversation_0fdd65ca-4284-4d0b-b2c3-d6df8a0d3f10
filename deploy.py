#!/usr/bin/env python3
"""
高考数据爬虫管理平台 - 部署脚本
支持开发环境、测试环境和生产环境的自动化部署
"""

import os
import sys
import subprocess
import shutil
import json
import argparse
from pathlib import Path
from datetime import datetime

class DeployManager:
    def __init__(self, environment='development'):
        self.environment = environment
        self.project_root = Path(__file__).parent
        self.backend_dir = self.project_root / 'backend'
        self.frontend_dir = self.project_root / 'frontend'
        self.deploy_dir = self.project_root / 'deploy'
        
        # 环境配置
        self.configs = {
            'development': {
                'backend_port': 8000,
                'frontend_port': 3000,
                'debug': True,
                'log_level': 'DEBUG'
            },
            'testing': {
                'backend_port': 8001,
                'frontend_port': 3001,
                'debug': False,
                'log_level': 'INFO'
            },
            'production': {
                'backend_port': 80,
                'frontend_port': 443,
                'debug': False,
                'log_level': 'WARNING'
            }
        }
    
    def print_banner(self):
        """打印部署横幅"""
        banner = f"""
╔══════════════════════════════════════════════════════════════╗
║                                                              ║
║           🚀 高考数据爬虫管理平台部署工具                      ║
║                                                              ║
║     环境: {self.environment.upper():<20} 时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S'):<20} ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝
        """
        print(banner)
    
    def check_prerequisites(self):
        """检查部署前置条件"""
        print("🔍 检查部署前置条件...")
        
        # 检查Python版本
        python_version = sys.version_info
        if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 8):
            print("❌ Python版本过低，需要Python 3.8+")
            return False
        print(f"✅ Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
        
        # 检查Node.js
        try:
            result = subprocess.run(['node', '--version'], capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✅ Node.js版本: {result.stdout.strip()}")
            else:
                print("❌ Node.js未安装")
                return False
        except FileNotFoundError:
            print("❌ Node.js未安装")
            return False
        
        # 检查项目结构
        required_dirs = ['backend', 'frontend']
        for dir_name in required_dirs:
            if not (self.project_root / dir_name).exists():
                print(f"❌ 缺少目录: {dir_name}")
                return False
        print("✅ 项目结构完整")
        
        return True
    
    def install_dependencies(self):
        """安装依赖"""
        print("\n📦 安装项目依赖...")
        
        # 安装后端依赖
        print("安装后端依赖...")
        try:
            subprocess.run([
                sys.executable, '-m', 'pip', 'install', '-r', 
                str(self.backend_dir / 'requirements.txt')
            ], check=True, cwd=self.backend_dir)
            print("✅ 后端依赖安装完成")
        except subprocess.CalledProcessError as e:
            print(f"❌ 后端依赖安装失败: {e}")
            return False
        
        # 安装前端依赖
        print("安装前端依赖...")
        try:
            # 检查包管理器
            package_manager = 'npm'
            try:
                subprocess.run(['pnpm', '--version'], check=True, capture_output=True)
                package_manager = 'pnpm'
            except (subprocess.CalledProcessError, FileNotFoundError):
                pass
            
            print(f"使用包管理器: {package_manager}")
            subprocess.run([package_manager, 'install'], check=True, cwd=self.frontend_dir)
            print("✅ 前端依赖安装完成")
        except subprocess.CalledProcessError as e:
            print(f"❌ 前端依赖安装失败: {e}")
            return False
        
        return True
    
    def build_frontend(self):
        """构建前端"""
        if self.environment == 'development':
            print("🏗️ 开发环境跳过前端构建")
            return True
        
        print("\n🏗️ 构建前端应用...")
        try:
            # 检查包管理器
            package_manager = 'npm'
            try:
                subprocess.run(['pnpm', '--version'], check=True, capture_output=True)
                package_manager = 'pnpm'
            except (subprocess.CalledProcessError, FileNotFoundError):
                pass
            
            subprocess.run([package_manager, 'run', 'build'], check=True, cwd=self.frontend_dir)
            print("✅ 前端构建完成")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ 前端构建失败: {e}")
            return False
    
    def setup_environment(self):
        """设置环境配置"""
        print(f"\n⚙️ 配置{self.environment}环境...")
        
        config = self.configs[self.environment]
        
        # 创建环境配置文件
        env_content = f"""# {self.environment.upper()} Environment Configuration
# Generated at {datetime.now().isoformat()}

# Backend Configuration
BACKEND_PORT={config['backend_port']}
DEBUG={config['debug']}
LOG_LEVEL={config['log_level']}

# Database Configuration
DATABASE_URL=sqlite:///./gaokao.db

# API Configuration
CRAWLER_API_TOKEN=your_api_token_here
API_BASE_URL=https://api.example.com

# Security
SECRET_KEY=your_secret_key_here
ALLOWED_HOSTS=localhost,127.0.0.1

# CORS Configuration
CORS_ORIGINS=http://localhost:{config['frontend_port']},http://127.0.0.1:{config['frontend_port']}
"""
        
        env_file = self.backend_dir / f'.env.{self.environment}'
        with open(env_file, 'w', encoding='utf-8') as f:
            f.write(env_content)
        
        print(f"✅ 环境配置文件已创建: {env_file}")
        
        # 创建前端环境配置
        frontend_env = f"""# Frontend Environment Configuration
VITE_API_BASE_URL=http://localhost:{config['backend_port']}
VITE_WS_BASE_URL=ws://localhost:{config['backend_port']}
VITE_APP_TITLE=高考数据爬虫管理平台
VITE_APP_VERSION=0.3.0
VITE_ENVIRONMENT={self.environment}
"""
        
        frontend_env_file = self.frontend_dir / f'.env.{self.environment}'
        with open(frontend_env_file, 'w', encoding='utf-8') as f:
            f.write(frontend_env)
        
        print(f"✅ 前端环境配置文件已创建: {frontend_env_file}")
        return True
    
    def create_deployment_scripts(self):
        """创建部署脚本"""
        print("\n📝 创建部署脚本...")
        
        # 创建部署目录
        self.deploy_dir.mkdir(exist_ok=True)
        
        # 创建启动脚本
        start_script = f"""#!/bin/bash
# 高考数据爬虫管理平台启动脚本 - {self.environment.upper()}

echo "🚀 启动高考数据爬虫管理平台 ({self.environment})"

# 设置环境变量
export ENVIRONMENT={self.environment}

# 启动后端服务
echo "启动后端服务..."
cd backend
python app/simple_main.py &
BACKEND_PID=$!
echo "后端服务PID: $BACKEND_PID"

# 等待后端启动
sleep 5

# 启动前端服务（仅开发环境）
if [ "{self.environment}" = "development" ]; then
    echo "启动前端开发服务..."
    cd ../frontend
    npm run dev &
    FRONTEND_PID=$!
    echo "前端服务PID: $FRONTEND_PID"
fi

echo "✅ 服务启动完成"
echo "后端地址: http://localhost:{self.configs[self.environment]['backend_port']}"
if [ "{self.environment}" = "development" ]; then
    echo "前端地址: http://localhost:{self.configs[self.environment]['frontend_port']}"
fi

# 等待用户中断
wait
"""
        
        start_script_file = self.deploy_dir / f'start_{self.environment}.sh'
        with open(start_script_file, 'w', encoding='utf-8') as f:
            f.write(start_script)
        
        # 设置执行权限
        os.chmod(start_script_file, 0o755)
        
        # 创建停止脚本
        stop_script = f"""#!/bin/bash
# 高考数据爬虫管理平台停止脚本

echo "🛑 停止高考数据爬虫管理平台服务"

# 停止Python进程
pkill -f "python.*simple_main.py"

# 停止Node.js进程（如果有）
pkill -f "node.*vite"

echo "✅ 服务已停止"
"""
        
        stop_script_file = self.deploy_dir / f'stop_{self.environment}.sh'
        with open(stop_script_file, 'w', encoding='utf-8') as f:
            f.write(stop_script)
        
        os.chmod(stop_script_file, 0o755)
        
        print(f"✅ 部署脚本已创建:")
        print(f"   启动脚本: {start_script_file}")
        print(f"   停止脚本: {stop_script_file}")
        
        return True
    
    def create_docker_files(self):
        """创建Docker配置文件"""
        if self.environment == 'development':
            print("🐳 开发环境跳过Docker配置")
            return True
        
        print("\n🐳 创建Docker配置文件...")
        
        # Dockerfile for backend
        backend_dockerfile = """FROM python:3.11-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \\
    gcc \\
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Expose port
EXPOSE 8000

# Run the application
CMD ["python", "app/simple_main.py"]
"""
        
        with open(self.backend_dir / 'Dockerfile', 'w') as f:
            f.write(backend_dockerfile)
        
        # Docker Compose
        docker_compose = f"""version: '3.8'

services:
  backend:
    build: ./backend
    ports:
      - "{self.configs[self.environment]['backend_port']}:8000"
    environment:
      - ENVIRONMENT={self.environment}
    volumes:
      - ./data:/app/data
    restart: unless-stopped

  frontend:
    build: ./frontend
    ports:
      - "{self.configs[self.environment]['frontend_port']}:80"
    depends_on:
      - backend
    restart: unless-stopped

volumes:
  data:
"""
        
        with open(self.project_root / 'docker-compose.yml', 'w') as f:
            f.write(docker_compose)
        
        print("✅ Docker配置文件已创建")
        return True
    
    def run_tests(self):
        """运行测试"""
        print("\n🧪 运行项目测试...")
        
        # 后端测试
        try:
            print("运行后端测试...")
            subprocess.run([
                sys.executable, '-m', 'pytest', 'tests/', '-v'
            ], check=True, cwd=self.backend_dir)
            print("✅ 后端测试通过")
        except subprocess.CalledProcessError:
            print("⚠️ 后端测试失败或未找到测试文件")
        except FileNotFoundError:
            print("⚠️ pytest未安装，跳过后端测试")
        
        # 前端测试
        try:
            print("运行前端测试...")
            subprocess.run(['npm', 'run', 'test'], check=True, cwd=self.frontend_dir)
            print("✅ 前端测试通过")
        except subprocess.CalledProcessError:
            print("⚠️ 前端测试失败或未配置测试")
        
        return True
    
    def deploy(self):
        """执行完整部署流程"""
        self.print_banner()
        
        steps = [
            ("检查前置条件", self.check_prerequisites),
            ("安装依赖", self.install_dependencies),
            ("构建前端", self.build_frontend),
            ("配置环境", self.setup_environment),
            ("创建部署脚本", self.create_deployment_scripts),
            ("创建Docker配置", self.create_docker_files),
            ("运行测试", self.run_tests)
        ]
        
        for step_name, step_func in steps:
            print(f"\n{'='*60}")
            print(f"执行步骤: {step_name}")
            print('='*60)
            
            if not step_func():
                print(f"\n❌ 部署失败于步骤: {step_name}")
                return False
        
        print(f"\n{'='*60}")
        print("🎉 部署完成!")
        print('='*60)
        print(f"环境: {self.environment}")
        print(f"后端端口: {self.configs[self.environment]['backend_port']}")
        print(f"前端端口: {self.configs[self.environment]['frontend_port']}")
        print(f"启动脚本: {self.deploy_dir}/start_{self.environment}.sh")
        print(f"停止脚本: {self.deploy_dir}/stop_{self.environment}.sh")
        
        if self.environment != 'development':
            print(f"Docker Compose: docker-compose up -d")
        
        return True

def main():
    parser = argparse.ArgumentParser(description='高考数据爬虫管理平台部署工具')
    parser.add_argument(
        '--env', 
        choices=['development', 'testing', 'production'],
        default='development',
        help='部署环境 (默认: development)'
    )
    parser.add_argument(
        '--skip-deps',
        action='store_true',
        help='跳过依赖安装'
    )
    parser.add_argument(
        '--skip-tests',
        action='store_true',
        help='跳过测试运行'
    )
    
    args = parser.parse_args()
    
    deployer = DeployManager(args.env)
    
    if args.skip_deps:
        deployer.install_dependencies = lambda: True
    
    if args.skip_tests:
        deployer.run_tests = lambda: True
    
    success = deployer.deploy()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
