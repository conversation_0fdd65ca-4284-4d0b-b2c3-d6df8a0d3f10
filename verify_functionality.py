#!/usr/bin/env python3
"""
高考项目功能验证脚本
验证所有核心功能是否正常工作
"""

import asyncio
import aiohttp
import json
import sys
from pathlib import Path

class FunctionalityVerifier:
    def __init__(self):
        self.base_url = "http://localhost:8000"
        self.results = []
        
    def print_banner(self):
        """打印验证横幅"""
        banner = """
╔══════════════════════════════════════════════════════════════╗
║                                                              ║
║           🔍 高考项目功能验证工具                              ║
║                                                              ║
║     验证所有核心功能是否正常工作                               ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝
        """
        print(banner)
    
    async def verify_api_health(self, session):
        """验证API健康状态"""
        try:
            async with session.get(f"{self.base_url}/api/health") as response:
                if response.status == 200:
                    data = await response.json()
                    self.add_result("API健康检查", True, f"状态: {data.get('status', 'unknown')}")
                    return True
                else:
                    self.add_result("API健康检查", False, f"HTTP {response.status}")
                    return False
        except Exception as e:
            self.add_result("API健康检查", False, f"连接失败: {e}")
            return False
    
    async def verify_crawler_config(self, session):
        """验证爬虫配置API"""
        try:
            async with session.get(f"{self.base_url}/api/crawler/config") as response:
                if response.status == 200:
                    data = await response.json()
                    self.add_result("爬虫配置API", True, "配置获取成功")
                    return True
                else:
                    self.add_result("爬虫配置API", False, f"HTTP {response.status}")
                    return False
        except Exception as e:
            self.add_result("爬虫配置API", False, f"请求失败: {e}")
            return False
    
    async def verify_websocket_endpoint(self, session):
        """验证WebSocket端点"""
        try:
            # 检查WebSocket连接统计
            async with session.get(f"{self.base_url}/api/ws/connections/stats") as response:
                if response.status == 200:
                    data = await response.json()
                    self.add_result("WebSocket端点", True, f"活跃连接: {data.get('active_connections', 0)}")
                    return True
                else:
                    self.add_result("WebSocket端点", False, f"HTTP {response.status}")
                    return False
        except Exception as e:
            self.add_result("WebSocket端点", False, f"请求失败: {e}")
            return False
    
    async def verify_task_management(self, session):
        """验证任务管理API"""
        try:
            async with session.get(f"{self.base_url}/api/crawler/tasks") as response:
                if response.status == 200:
                    data = await response.json()
                    task_count = len(data.get('data', []))
                    self.add_result("任务管理API", True, f"任务数量: {task_count}")
                    return True
                else:
                    self.add_result("任务管理API", False, f"HTTP {response.status}")
                    return False
        except Exception as e:
            self.add_result("任务管理API", False, f"请求失败: {e}")
            return False
    
    async def verify_data_apis(self, session):
        """验证数据查询API"""
        endpoints = [
            ("/api/universities", "大学数据API"),
            ("/api/majors", "专业数据API"),
            ("/api/statistics/overview", "统计数据API")
        ]
        
        for endpoint, name in endpoints:
            try:
                async with session.get(f"{self.base_url}{endpoint}") as response:
                    if response.status == 200:
                        data = await response.json()
                        count = len(data.get('data', []))
                        self.add_result(name, True, f"数据条数: {count}")
                    else:
                        self.add_result(name, False, f"HTTP {response.status}")
            except Exception as e:
                self.add_result(name, False, f"请求失败: {e}")
    
    def verify_frontend_files(self):
        """验证前端文件"""
        frontend_dir = Path("frontend")
        if not frontend_dir.exists():
            self.add_result("前端目录", False, "frontend目录不存在")
            return False
        
        # 检查关键文件
        key_files = [
            "package.json",
            "src/main.ts",
            "src/App.vue",
            "src/views/Crawler.vue",
            "src/components/EnhancedCrawlerDashboard.vue",
            "src/components/CrawlerConfigEditor.vue",
            "src/components/CrawlerScheduleManager.vue",
            "src/components/SystemHealthMonitor.vue",
            "src/components/DataExportManager.vue",
            "src/components/UserPreferences.vue"
        ]
        
        missing_files = []
        for file_path in key_files:
            if not (frontend_dir / file_path).exists():
                missing_files.append(file_path)
        
        if missing_files:
            self.add_result("前端文件检查", False, f"缺失文件: {', '.join(missing_files)}")
            return False
        else:
            self.add_result("前端文件检查", True, f"所有关键文件存在 ({len(key_files)}个)")
            return True
    
    def verify_backend_files(self):
        """验证后端文件"""
        backend_dir = Path("backend")
        if not backend_dir.exists():
            self.add_result("后端目录", False, "backend目录不存在")
            return False
        
        # 检查关键文件
        key_files = [
            "requirements.txt",
            "app/simple_main.py",
            "app/core/config.py",
            "app/core/simple_database.py",
            "app/api/crawler.py",
            "app/api/websocket.py"
        ]
        
        missing_files = []
        for file_path in key_files:
            if not (backend_dir / file_path).exists():
                missing_files.append(file_path)
        
        if missing_files:
            self.add_result("后端文件检查", False, f"缺失文件: {', '.join(missing_files)}")
            return False
        else:
            self.add_result("后端文件检查", True, f"所有关键文件存在 ({len(key_files)}个)")
            return True
    
    def add_result(self, test_name, success, message):
        """添加测试结果"""
        status = "✅ 通过" if success else "❌ 失败"
        self.results.append({
            "name": test_name,
            "success": success,
            "status": status,
            "message": message
        })
        print(f"{status} {test_name}: {message}")
    
    def print_summary(self):
        """打印测试总结"""
        total = len(self.results)
        passed = sum(1 for r in self.results if r["success"])
        failed = total - passed
        
        print("\n" + "="*60)
        print("📊 验证结果总结")
        print("="*60)
        print(f"总测试项: {total}")
        print(f"通过: {passed}")
        print(f"失败: {failed}")
        print(f"成功率: {(passed/total*100):.1f}%")
        
        if failed > 0:
            print("\n❌ 失败的测试项:")
            for result in self.results:
                if not result["success"]:
                    print(f"  - {result['name']}: {result['message']}")
        
        print("\n" + "="*60)
        
        if failed == 0:
            print("🎉 所有功能验证通过！项目运行正常。")
        else:
            print("⚠️ 部分功能存在问题，请检查上述失败项。")
        
        return failed == 0
    
    async def run_verification(self):
        """运行完整验证"""
        self.print_banner()
        
        print("🔍 开始功能验证...")
        print()
        
        # 文件系统检查
        print("📁 检查项目文件...")
        self.verify_frontend_files()
        self.verify_backend_files()
        print()
        
        # API检查
        print("🌐 检查API服务...")
        async with aiohttp.ClientSession() as session:
            # 基础健康检查
            api_healthy = await self.verify_api_health(session)
            
            if api_healthy:
                # 如果API健康，继续其他检查
                await self.verify_crawler_config(session)
                await self.verify_websocket_endpoint(session)
                await self.verify_task_management(session)
                await self.verify_data_apis(session)
            else:
                print("⚠️ API服务不可用，跳过API相关测试")
        
        print()
        return self.print_summary()

async def main():
    """主函数"""
    verifier = FunctionalityVerifier()
    success = await verifier.run_verification()
    
    if success:
        print("\n🚀 项目已准备就绪，可以正常使用！")
        print("📖 访问地址:")
        print("   - 前端界面: http://localhost:3001")
        print("   - API文档: http://localhost:8000/docs")
        print("   - 健康检查: http://localhost:8000/api/health")
        sys.exit(0)
    else:
        print("\n🔧 请修复上述问题后重新验证。")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
